package com.altomni.apn.job.web.rest.job;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.company.CompanyBriefDTO;
import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.*;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.common.dto.job.FindPrivateJobByIdsDTO;
import com.altomni.apn.job.domain.job.RelateJobFolderInfo;
import com.altomni.apn.job.domain.report.ReportUserJobTalent;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.service.agency.AgencyService;
import com.altomni.apn.job.service.async.AsyncRecordService;
import com.altomni.apn.job.service.dto.job.*;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.job.ColumnPreferenceService;
import com.altomni.apn.job.service.job.GpuGrpcService;
import com.altomni.apn.job.service.job.JobService;
import com.altomni.apn.job.service.job.SearchPreferenceService;
import com.altomni.apn.job.service.xxljob.XxlJobService;
import com.altomni.apn.job.web.rest.vm.CompanyVM;
import com.altomni.apn.job.web.rest.vm.MyApplication;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * REST controller for managing Job.
 */
@Api(tags = {"Job", "ATS-Jobs"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class JobResource {

    @Resource
    private JobService jobService;

    @Resource
    private AsyncRecordService asyncRecordService;

    @Resource
    private ColumnPreferenceService columnPreferenceService;

    @Resource
    private SearchPreferenceService searchPreferenceService;

    @Resource
    private EsFillerJobService esFillerJobService;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private AgencyService agencyService;

    private static final String ENTITY_NAME = "job";

    @ApiOperation(value = "Search for jobs")
    @PostMapping(value = "/jobs/search", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<String> searchJob(@RequestBody SearchConditionDTO searchDTO, Pageable pageable) throws Throwable {
        log.info("({},{}) REST request to search job:  {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), searchDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        String json = jobService.searchJob(searchDTO, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(json);
    }

    @ApiOperation(value = "Search for relate job folders")
    @PostMapping(value = "/relate-job-folders/search", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<List<RelateJobFolderInfo>> searchRelateJobFolders(@RequestBody SearchConditionDTO searchDTO, Pageable pageable) throws Throwable {
        log.info("({},{}) REST request to search relate job folders:  {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), searchDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<RelateJobFolderInfo> folderInfoList = jobService.searchRelateJobFolders(searchDTO, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(folderInfoList);
    }

    /**
     * POST  /jobs : Create a new job.
     *
     * @param jobDTO the job to create
     * @return the ResponseEntity with status 201 (Created) and with body the new job, or with status 400 (Bad Request) if the job has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create a new job", notes = "The job owner will be current user. Creating job comes from parser parsing JD or from ATS. For parser, the job search string and " +
            "skill string will also be saved.")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/jobs/old")
    @NoRepeatSubmit(expire = 3600000) //1小时
    public ResponseEntity<JobDTOV3> createJobOld(@Valid @RequestBody JobDTOV3 jobDTO) throws URISyntaxException, IOException {
        log.info("[APN: Job @{}] REST request to save Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobDTO);
        if (jobDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobType jobType = esFillerJobService.getJobTypeByRecruitmentProcessId(jobDTO.getRecruitmentProcess().getId());
        CompletableFuture.runAsync(() -> columnPreferenceService.updateByUserId(SecurityUtils.getUserId(), jobType, ModuleType.JOB));
        JobDTOV3 result = jobService.formatAndSave(jobDTO, false);
        jobDTO.setId(result.getId());
        jobService.syncIpgJob(jobDTO, result);
        xxlJobService.addCandidateReminderForJob(result.getId());
        return ResponseEntity.created(new URI("/api/v3/jobs/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(ServiceUtils.convert2DTO(result, JobDTOV3.class));
    }

    /**
     * POST  /jobs : Create a new job.
     *
     * @param jobDTO the job to create
     * @return the ResponseEntity with status 201 (Created) and with body the new job, or with status 400 (Bad Request) if the job has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create a new job", notes = "The job owner will be current user. Creating job comes from parser parsing JD or from ATS. For parser, the job search string and " +
            "skill string will also be saved.")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/jobs")
    @NoRepeatSubmit(expire = 3600000) //1小时
    public ResponseEntity<JobDTOV3> createJob(HttpServletRequest request) throws URISyntaxException, IOException, Exception {
        String requestBody = getRequestBody(request);
        log.info("[APN: Job @{}] REST request to save Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), requestBody);

        JobDTOV3 jobDTO = jobService.translate2JobDTOV3(requestBody);

        if (jobDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobType jobType = esFillerJobService.getJobTypeByRecruitmentProcessId(jobDTO.getRecruitmentProcess().getId());
        CompletableFuture.runAsync(() -> columnPreferenceService.updateByUserId(SecurityUtils.getUserId(), jobType, ModuleType.JOB));
        JobDTOV3 result = jobService.formatAndSave(jobDTO, false);
        jobDTO.setId(result.getId());
        jobService.syncIpgJob(jobDTO, result);
        xxlJobService.addCandidateReminderForJob(result.getId());
        return ResponseEntity.created(new URI("/api/v3/jobs/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(ServiceUtils.convert2DTO(result, JobDTOV3.class));
    }



    private String getRequestBody(HttpServletRequest request) throws IOException {
        // 首先检查是否已经缓存了请求体
        String cachedBody = (String) request.getAttribute("cachedRequestBody");
        if (cachedBody != null) {
            return cachedBody;
        }

        // 否则读取请求体
        StringBuilder buffer = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }

        // 缓存请求体
        cachedBody = buffer.toString();
        request.setAttribute("cachedRequestBody", cachedBody);

        return cachedBody;
    }

    @ApiOperation(value = "Create a new private job", notes = "The job owner will be current user.")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/jobs/private")
    @NoRepeatSubmit
    public ResponseEntity<JobDTOV3> createPrivateJob(@Valid @RequestBody JobDTOV3 jobDTO) throws URISyntaxException, IOException {
        log.info("[APN: Job @{}] REST request to save private Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobDTO);
        if (jobDTO.getId() != null) {
            throw new CustomParameterizedException(
                    commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(
                            JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(),
                            CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                            jobApiPromptProperties.getJobService()));
        }
        JobType jobType = esFillerJobService.getJobTypeByRecruitmentProcessId(jobDTO.getRecruitmentProcess().getId());
        CompletableFuture.runAsync(() -> columnPreferenceService.updateByUserId(SecurityUtils.getUserId(), jobType, ModuleType.PRIVATE_JOB));
        JobDTOV3 result = jobService.formatAndSave(jobDTO, true);
        xxlJobService.addCandidateReminderForJob(result.getId());
        return ResponseEntity.created(new URI("/api/v3/jobs/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(ServiceUtils.convert2DTO(result, JobDTOV3.class));
    }

    /**
     * PUT  /jobs : Updates an existing job.
     *
     * @param id     the id of the job to update
     * @param jobDTO the job to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated job,
     * or with status 400 (Bad Request) if the job is not valid,
     * or with status 500 (Internal Server Error) if the job couldn't be updated
     */
    @ApiOperation(value = "Update job by job id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/{id}/old")
    @NoRepeatSubmit
    public ResponseEntity<JobDTOV3> updateJobOld(@ApiParam(value = "job id", required = true) @PathVariable Long id, @Valid @RequestBody JobDTOV3 jobDTO) throws IOException {
        jobDTO.setId(id);
        log.info("[APN: Job @{}] REST request to update Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobDTO);
        JobDTOV3 result = jobService.update(jobDTO);
        jobDTO.setId(result.getId());
        jobService.syncIpgJob(jobDTO, result);
        xxlJobService.updateCandidateReminderForJob(result.getId());
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(ServiceUtils.convert2DTO(result, JobDTOV3.class));
    }

    @PutMapping("/jobs/{id}")
    @NoRepeatSubmit
    public ResponseEntity<JobDTOV3> updateJob(@ApiParam(value = "job id", required = true) @PathVariable Long id, HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        log.info("[APN: Job @{}] REST request to update Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), requestBody);
        JobDTOV3 jobDTO = jobService.translate2JobDTOV3(requestBody);

        jobDTO.setId(id);
        JobDTOV3 result = jobService.update(jobDTO);
        jobDTO.setId(result.getId());
        jobService.syncIpgJob(jobDTO, result);
        xxlJobService.updateCandidateReminderForJob(result.getId());
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(ServiceUtils.convert2DTO(result, JobDTOV3.class));
    }


    /*
    INTERNAL USE, DO NOT DELETE
     */
    @ApiOperation(value = "Delete job by job id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @DeleteMapping("/jobs/{id}")
    @NoRepeatSubmit
    public ResponseEntity<String> deleteJob(@ApiParam(value = "job id", required = true) @PathVariable Long id, @RequestParam(value = "tenantId") Long tenantId) throws IOException {
        log.info("[APN: Job @{}] REST request to delete Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id);
        String result = jobService.deleteJob(id, tenantId);

        return ResponseEntity.ok().body(result);
    }


    @ApiOperation(value = "Update private job by job id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/{id}/private")
    @NoRepeatSubmit
    public ResponseEntity<JobDTOV3> updatePrivateJob(@ApiParam(value = "job id", required = true) @PathVariable Long id, @Valid @RequestBody JobDTOV3 jobDTO) throws IOException {
        jobDTO.setId(id);
        log.info("[APN: Job @{}] REST request to update Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobDTO);
        JobDTOV3 result = jobService.updatePrivateJob(jobDTO);
        xxlJobService.updateCandidateReminderForJob(result.getId());
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(ServiceUtils.convert2DTO(result, JobDTOV3.class));
    }

    /**
     * PUT  /jobs : Updates job status.
     *
     * @param id     the id of the job to update
     * @param status the job status
     * @return the ResponseEntity with status 200 (OK) and with body the updated job,
     * or with status 400 (Bad Request) if the job is not valid,
     * or with status 500 (Internal Server Error) if the job couldn't be updated
     */
    @ApiOperation(value = "Update job status by job id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/{id}/{status}")
    @NoRepeatSubmit
    public ResponseEntity<JobDTOV3> updateStatus(@ApiParam(value = "job id", required = true) @PathVariable Long id, @ApiParam(value = "job status", required = true) @PathVariable JobStatus status) throws IOException {
        log.info("[APN: Job @{}] REST request to update Job: {} status : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id, status);
        StopWatch stopWatch = new StopWatch("echocheng2 start");
        stopWatch.start("[echocheng 2.1] updateStatus");
        JobDTOV3 result = jobService.updateStatus(id, status);
        stopWatch.stop();
        if (result == null){
            return ResponseEntity.notFound().build();
        }
        SecurityContext context = SecurityContextHolder.getContext();
        stopWatch.start("[echocheng 2.2] CompletableFuture.runAsync");
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            esFillerJobService.syncJobStatusToEsV3(SecurityUtils.getTenantId(), id, status);
            xxlJobService.updateReminderByJobStatusAndIds(status, CollUtil.newArrayList(id));
            agencyService.updateAgencySharedStatusByJobId(id);
        });
        stopWatch.stop();
        log.info("[apn @{}] echocheng2 time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation(value = "Update job status by job id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/{id}/{status}/private")
    @NoRepeatSubmit
    public ResponseEntity<JobDTOV3> updatePrivateJobStatus(@ApiParam(value = "job id", required = true) @PathVariable Long id, @ApiParam(value = "job status", required = true) @PathVariable JobStatus status) throws IOException {
        log.info("[APN: Job @{}] REST request to update private Job: {} status : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id, status);
        JobDTOV3 result = jobService.updatePrivateJobStatus(id, status);
        if (result == null){
            return ResponseEntity.notFound().build();
        }
        esFillerJobService.syncJobStatusToEsV3(SecurityUtils.getTenantId(), id, status);
        xxlJobService.updateReminderByJobStatusAndIds(status, CollUtil.newArrayList(id));
        agencyService.updateAgencySharedStatusByJobId(id);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * PUT  /jobs : Updates jobs status.
     *
     * @param updateJobsStatusDTO
     * @return the ResponseEntity with status 200 (OK) and with body the updated job,
     * or with status 400 (Bad Request) if the job is not valid,
     * or with status 500 (Internal Server Error) if the job couldn't be updated
     */
    @ApiOperation(value = "Update job status by job id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/update-job-status")
    @NoRepeatSubmit
    @Deprecated
    public ResponseEntity<List<JobDTOV3>> updateJobsStatus(@Valid @RequestBody UpdateJobsStatusDTO updateJobsStatusDTO) throws IOException {
        log.info("[APN: Job @{}] REST request to update Jobs", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        List<JobDTOV3> result = jobService.updateJobsStatus(updateJobsStatusDTO);
        if (result == null){
            return ResponseEntity.notFound().build();
        }
        result.forEach(jobDTO -> esFillerJobService.syncJobStatusToEs(SecurityUtils.getTenantId(), jobDTO.getId(), jobDTO.getStatus()));
        xxlJobService.updateReminderByJobStatusAndIds(updateJobsStatusDTO.getToStatus(), updateJobsStatusDTO.getJobIds());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @GetMapping("/jobs/without-entity/{jobId}")
    public ResponseEntity<JobDTOV3> getJobWithoutEntity(@ApiParam(value = "job id", required = true) @PathVariable("jobId") Long jobId) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobId);
        JobDTOV3 result = jobService.getJobWithoutEntity(jobId);
        return ResponseEntity.ok(result);
    }

    /**
     * GET  /dict : get all user job column Preference by userId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of user Preferences in body
     */
    @ApiOperation(value = "Get/Filter all user Preference by userId ")
    @GetMapping("/jobs/preference/{module}")
    public ResponseEntity<ColumnPreferenceDTO> findUserPreference(@ApiParam(value = "module", required = true) @PathVariable ModuleType module) throws IOException {
        log.info("[APN: UserPreference @{}] REST request to get user Preference config : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), module);
        ColumnPreferenceDTO result = columnPreferenceService.findOne(com.altomni.apn.common.utils.SecurityUtils.getUserId(), module);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * POST  /jobs : Create a new user job column Preference .
     *
     * @param jobColumnPreferenceDTO the user Preference to create
     * @return the ResponseEntity with status 201 (Created) and with body the new user Preference config.
     */
    @ApiOperation(value = "Create a new user search conifg", notes = " ")
    @PostMapping("/jobs/preference")
    public ResponseEntity<Void> createUserPreference(@Valid @RequestBody ColumnPreferenceDTO jobColumnPreferenceDTO) {
        log.info("[APN: UserPreference @{}] REST request to save user Preference config : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobColumnPreferenceDTO);
        columnPreferenceService.create(jobColumnPreferenceDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * GET  /dict : get all user searchPreference config by userId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of user searchPreference config in body
     */
    @ApiOperation(value = "Get/Filter all user search preference record by userId and searchName ")
    @GetMapping("/jobs/search/config/{module}")
    public ResponseEntity<List<SearchPreferenceDTO>> findAllByCode(@PathVariable(value = "module") ModuleType module, @RequestParam(required = false) String searchName) {
        log.info("[APN: SearchPreference @{}] REST request to get user Preference config : {}{} ", com.altomni.apn.common.utils.SecurityUtils.getUserId(), module, searchName);
        List<SearchPreferenceDTO> result = searchPreferenceService.findByUserId(com.altomni.apn.common.utils.SecurityUtils.getUserId(), searchName, module);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * POST  /jobs : Create a new user searchPreference .
     *
     * @param searchPreferenceDTO the user Preference to create
     * @return the ResponseEntity with status 201 (Created) and with body the new user searchPreference .
     */
    @ApiOperation(value = "Create a new user search conifg", notes = " ")
    @PostMapping("/jobs/search/config")
    @NoRepeatSubmit
    public ResponseEntity<SearchPreferenceDTO> createSearchConfig(@Valid @RequestBody SearchPreferenceDTO searchPreferenceDTO) throws IOException {
        log.info("[APN: SearchPreference @{}] REST request to save user SearchPreference  : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), searchPreferenceDTO);
        SearchPreferenceDTO result = searchPreferenceService.create(searchPreferenceDTO);
        return ResponseEntity.ok(result);
    }

    /**
     * {@code DELETE  /jobs/search/:id} : Delete the user searchPreference config by id .
     *
     * @param id the id of the user searchPreference config to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/jobs/search/{id}")
    public ResponseEntity<Void> deleteById(@PathVariable Long id) {
        log.info("[APN: SearchPreference @{}] REST request to delete user searchPreference config : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id);
        searchPreferenceService.deleteById(id);
        return ResponseEntity.ok().build();
    }

    /**
     * {@code DELETE  /jobs/search} : Delete All of the user searchPreference config  .
     *
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/jobs/search")
    public ResponseEntity<Void> deleteAllByUserId() {
        log.info("[APN: SearchPreference @{}] REST request to delete all of user searchPreference config ", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        searchPreferenceService.deleteAllByUserId(com.altomni.apn.common.utils.SecurityUtils.getUserId());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "get HR info", notes = " ")
    @GetMapping("/jobs/contact/{company}")
    public ResponseEntity<List<JobCompanyContactRelationVO>> getJobHrInfo(@PathVariable("company") Long companyId) {
        List<JobCompanyContactRelationVO> result = jobService.getJobHrInfo(companyId);
        return ResponseEntity.ok().body(result);
    }


    @GetMapping("/dashboard/my-jobs/company")
    public ResponseEntity<List<CompanyVM>> getDashboardMyJobsCompany(
            @ApiParam(value = "query time from (UNIX timestamp)") @RequestParam Long from,
            @ApiParam(value = "query time to (UNIX timestamp)") @RequestParam Long to,
            @ApiParam(value = "if only show jobs related to me. Whether the job is related to me depends on whether I have submitted " +
                    "any applications to the job as a recruiter or sourcer.") @RequestParam(defaultValue = "false") Boolean myJobsOnly,
            Pageable pageable) {
        log.info("[APN: Job/Dashboard @{}] REST request to get all companies: from {} to {} (UNIX timestamp)", com.altomni.apn.common.utils.SecurityUtils.getUserId(), from, to);
        List<CompanyVM> companyVMList = jobService.findAllCompanyForDashboard(myJobsOnly, Instant.ofEpochSecond(from), Instant.ofEpochSecond(to));
        return new ResponseEntity<>(companyVMList, HttpStatus.OK);
    }


    /**
     * {@code GET  /dashboard/my-jobs/:id} : Return the list of applications of the job for 'My Jobs' module application statistics detail info in dashboard
     *
     * @param id          PathVariable the applications associated job id
     * @param status      PathVariable the application status of the job to query
     * @param relatedToMe PathVariable if only show the applications related to me
     * @param pageable    (optional) limit the size, the maximum size is defined in property file
     * @return the ResponseEntity with status 200 (OK) and with body the my application list which could be null
     */
    @ApiOperation(value = "Get 'My Jobs' details data (applications) for Dashboard", notes = "'My Jobs' is a module of dashboard. " +
            "The result is all applications of a certain job in specific status and the relation to me. The applications may not " +
            "belong to/related to me, it is determined by the query param 'relatedToMe'.")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/dashboard/my-jobs/{id}")
    @ProcessConfidentialTalent()
    public ResponseEntity<List<MyApplication>> getDashboardMyJobsDetails(
            @ApiParam(value = "the job id") @PathVariable Long id,
            @ApiParam(value = "application status") @RequestParam NodeType status,
            @ApiParam(value = "if the application related to me. Whether the job is related to me depends on whether I have " +
                    "submitted the application as a recruiter or sourcer.") @RequestParam(defaultValue = "false") boolean relatedToMe,
            Pageable pageable) {
        log.info("[APN: Job/Dashboard @{}] REST request to get all applications details of jobId: {}, applicationStatus: {}, relatedToMe: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id, status, relatedToMe);
        List<MyApplication> myJobs = jobService.findAllMyApplicationsForDashboard(id, status, relatedToMe);
        return new ResponseEntity<>(myJobs, HttpStatus.OK);
    }

    /**
     * POST  /jobs/jobIds : get all my jobs.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of jobs in body
     */
    @PostMapping("/jobs/jobIds")
    public ResponseEntity<List<ResignUserReportJobDTO>> getJobsByIds(@RequestBody ReportUserJobTalent userJobTalent) {
        log.info("[APN: Job @{}] REST request to get jobs by ids: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), userJobTalent);
        List<ResignUserReportJobDTO> result = jobService.findAllByIds(Stream.of(userJobTalent.getJobIds().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * get job company source data
     */
    @GetMapping("/job-countries")
    public ResponseEntity<Set<String>> getJobCountries() {
        log.info("[APN: Job @{}] REST request to get all job countries", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        return ResponseEntity.ok(jobService.getJobCountries());
    }

    @GetMapping("/user-countries")
    public ResponseEntity<Set<String>> getUserCountries() {
        log.info("[APN: Job @{}] REST request to get all job countries", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        return ResponseEntity.ok(jobService.getUserCountries());
    }

    /**
     * get job company source data
     */
    @GetMapping("/job-companies")
    public ResponseEntity<List<CompanyBriefDTO>> getJobCompanies() {
        log.info("[APN: Job @{}] REST request to get all job companies", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        return ResponseEntity.ok(jobService.getJobCompanies());
    }


    @GetMapping("/dashboard/dormant/jobs")
    public ResponseEntity<List<IDormantJobDTO>> getAllDormantJobsForDashboard() {
        log.info("[APN: DormantActivityMonitor @{}] REST request to get all dormant jobs for dashboard}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        List<IDormantJobDTO> res = jobService.findAllDormantJobsByAmId(com.altomni.apn.common.utils.SecurityUtils.getTenantId(), com.altomni.apn.common.utils.SecurityUtils.getUserId());
        return ResponseEntity.ok(res);
    }

    @GetMapping("/jobs/{id}")
    public ResponseEntity<JobDTOV3> findById(@PathVariable("id") Long jobId) {
        log.info("({},{}) REST request to get job by id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), jobId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobService.findOneWithEntity(jobId)));
    }

    @GetMapping("/jobs/{id}/{tenantId}")
    public ResponseEntity<JobDTOV3> findById(@PathVariable("id") Long jobId,@PathVariable("tenantId") Long tenantId) {
        log.info("({},{}) REST request to get job by id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), jobId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobService.findOneWithEntity(jobId,tenantId)));
    }

    @GetMapping("/jobs/{id}/private")
    public ResponseEntity<JobDTOV3> findPrivateJobById(@PathVariable("id") Long jobId) {
        log.info("({},{}) REST request to get private job by id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), jobId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobService.findOnePrivateJobWithEntity(jobId)));
    }

    @PostMapping("/jobs/search-private-by-id")
    public ResponseEntity<List<Long>> findPrivateJobByIds(FindPrivateJobByIdsDTO dto) {
        log.info("({},{}) REST request to get private job by id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), dto);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobService.findPrivateJobByIds(dto)));
    }

    @GetMapping("/jobs/brief/{id}")
    public ResponseEntity<JobDTOV3> findByIdNoEntity(@PathVariable("id") Long jobId) {
        log.info("({},{}) REST request to get job brief by id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), jobId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobService.findOneBrief(jobId)));
    }

    @GetMapping("/jobs/no-token/{id}")
    public ResponseEntity<JobDTOV3> findByIdNoToken(@PathVariable("id") Long jobId) {
        log.info("REST request to get job by id: {}", jobId);
        return ResponseEntity.ok(jobService.findOneWithEntityNoToken(jobId));
    }

    @DeleteMapping("/jobs/async-record/{asyncEnum}")
    public ResponseEntity<Void> clearAllSyncRecordError(@PathVariable("asyncEnum") AsyncEnum asyncEnum, @RequestBody List<Long> successIds) {
        log.info("({},{}) REST request to clear all sync record error for asyncEnum: {} , and successIds: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), asyncEnum, successIds);
        asyncRecordService.clearAllSyncRecordError(asyncEnum, successIds);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/jobs/async-record")
    public ResponseEntity<AsyncRecord> saveAsyncRecord(@RequestBody AsyncRecord asyncRecord) {
//        log.info("({},{}) REST request to save asyncRecord: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), asyncRecord);
        return ResponseEntity.ok(asyncRecordService.save(asyncRecord));
    }

    @GetMapping("/jobs/async-record/async-type/{asyncEnum}/talent/{talentId}/count-sync-error")
    public ResponseEntity<Integer> countSyncError(@PathVariable("asyncEnum") AsyncEnum asyncEnum, @PathVariable("talentId") Long talentId) {
        log.info("({},{}) REST request to count sync error for asyncEnum: {}, and talentId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), asyncEnum, talentId);
        return ResponseEntity.ok(asyncRecordService.countSyncError(asyncEnum, talentId));
    }

    @PutMapping("/jobs/async-record/update-failure-talent-to-success")
    public ResponseEntity<Void> updateSyncFailureTalentsToSuccess(@RequestBody List<Long> succeedIds) {
        log.info("({},{}) REST request to update sync failure talents to success: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), succeedIds);
        asyncRecordService.updateSyncFailureToSuccess(AsyncEnum.DATA_TYPE_TALENT, succeedIds);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/jobs/column-preference/user/{userId}/creation-talent-type/{type}/module-type/{moduleType}")
    public ResponseEntity<Void> updateColumnPreferenceByUserId(@PathVariable("userId") Long userId, @PathVariable("type") CreationTalentType type, @PathVariable("moduleType") ModuleType moduleType) {
        log.info("({},{}) REST request to update column preference by userId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), userId, type, moduleType);
        columnPreferenceService.updateByUserId(userId, type, moduleType);
        return ResponseEntity.ok().build();
    }

    /**
     * ------------------------------------------- For Parser Use ONLY -------------------------------------------
     */

    @PostMapping("/jobs/format-jd")
    @Timed
    public ResponseEntity<String> formatJD(@RequestBody String data) {
//        log.info("[APN: JobResourceV3 @{}] Request to format JD: {}", SecurityUtils.getUserId(), data);
        return ResponseEntity.ok(jobService.formatJd(data));
    }

    /**
     * ------------------------------------------- For ipg Use ONLY -------------------------------------------
     */
    @ApiOperation(value = "Synchronize a job to IPG")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/jobs/sync/toIpg")
    @Timed
    public ResponseEntity<HttpResponse> syncJobToIpg(@Valid @RequestBody JobToIpgDTO jobDTO) throws URISyntaxException, IOException {
        log.info("[APN: Job @{}] REST request to synchronize a job to IPG : {}", SecurityUtils.getUserId(), jobDTO);
        if (jobDTO.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        HttpResponse res = jobService.syncJobToIpg(jobDTO);
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "Update a job synchronized to IPG by jobId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/sync/toIpg/{id}")
    @Timed
    public ResponseEntity<HttpResponse> updateSyncJobToIpg(@ApiParam(value = "jobId", required = true) @PathVariable Long id,
                                                           @Valid @RequestBody JobToIpgDTO jobDTO) throws URISyntaxException, IOException {
        jobDTO.setId(id);
        log.info("[APN: Job @{}] REST request to update a job synchronized to IPG by jobId : {}", SecurityUtils.getUserId(), jobDTO);
        if (jobDTO.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        HttpResponse res = jobService.updateSyncJobToIpg(jobDTO);
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "Delete a job synchronized to IPG by jobId logically")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PutMapping("/jobs/sync/toIpg/{id}/{status}")
    @Timed
    public ResponseEntity<HttpResponse> deleteSyncJobToIpg(@ApiParam(value = "jobId", required = true) @PathVariable Long id,
                                                           @ApiParam(value = "status", required = true) @PathVariable JobStatus status) throws URISyntaxException, IOException {
        log.info("[APN: Job @{}] REST request to delete a job synchronized to IPG by jobId : {}", SecurityUtils.getUserId(), id);
        if (id == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        HttpResponse res = jobService.deleteSyncJobToIpg(id, status);
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "Query a job synchronized to IPG by jobId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/jobs/sync/toIpg/{id}")
    @Timed
    public ResponseEntity<HttpResponse> querySyncJobToIpg(@ApiParam(value = "jobId", required = true) @PathVariable Long id) throws URISyntaxException, IOException {
        log.info("[APN: Job @{}] REST request to query a job synchronized to IPG by jobId : {}", SecurityUtils.getUserId(), id);
        if (id == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        HttpResponse res = jobService.querySyncJobToIpg(id);
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "when numberOfOfferAccepted == job.getOpengings then update job and close ipgJob")
    @PutMapping("/jobs/number-of-offer-accepted-equals-opengings/{jobId}/{status}")
    @Timed
    public void numberOfOfferAcceptedEqualsOpenings(@PathVariable("jobId") Long jobId,
                                                    @PathVariable("status") Integer status) throws IOException {
        log.info("[APN: Job @{}] REST request to numberOfOfferAcceptedEqualsOpenings by jobId : {}, status : {}", SecurityUtils.getUserId(), jobId, status);
        if (jobId == null || status == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        jobService.numberOfOfferAcceptedEqualsOpenings(jobId, status);
        xxlJobService.updateReminderByJobStatusAndIds(JobStatus.fromDbValue(status), CollUtil.newArrayList(jobId));
    }

    /**
     * hot list
     */
    @GetMapping("/jobs/hotlist/{hotListId}")
    public ResponseEntity<List<JobV3>> findAllByHotListId(@PathVariable("hotListId") Long hotListId){
        log.info("({},{}) REST request to find all by pcode order by sort order: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), hotListId);
        return ResponseEntity.ok(jobService.findAllByHotListId(hotListId));
    }

    /**
     * The interface is only used to update the historical data format saved by the/search/config interface.
     * @return
     */
    @GetMapping("/jobs/search/preference/format/update")
    public ResponseEntity<Object> searchPreferenceFormatUpdate() {
        return ResponseEntity.ok(searchPreferenceService.searchPreferenceFormatUpdate());
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }

    /***
     * Job V3 statistics and search history
     * @return
     * @throws Throwable
     */
    @ApiOperation(value = "Count jobs")
    @GetMapping(value = "/jobs/statistics")
    public ResponseEntity<List<SearchCategoryCountDTO>> getJobStatistics() throws Throwable {
        log.info("({},{}) REST request to get Job Statistics:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        List<SearchCategoryCountDTO> jobStatistics= jobService.getJobSearchCategoryStatistics();
        return ResponseEntity.ok(jobStatistics);
    }

    /***
     * Query Job search history
     * @return
     */
    @GetMapping("/jobs/search/history")
    @ApiOperation(value = "query job search history")
    public ResponseEntity<List<String>> querySearchHistory() {
        log.info("[APN: Job Search History @{}] REST request to query job search history.", SecurityUtils.getUserId());
        List<String> searchHistoryList = jobService.querySearchHistory();
        return new ResponseEntity<>(searchHistoryList, HttpStatus.OK);
    }

    @PostMapping("/jobs/search/historyTestInsert")
    public ResponseEntity<HttpStatus> testSearch(@RequestBody SearchConditionDTO searchDTO) {
        log.info("[APN: Job search History test @{}] REST request to test job search history.", SecurityUtils.getUserId());
        jobService.insertSearchHistoryDummy(searchDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/jobs/{jobId}/job-activities")
    @ApiOperation(value = "query job search history")
    public ResponseEntity<List<JobActivityDTO>> getJobActivities(@PathVariable("jobId") Long jobId, @ApiParam Pageable pageable) throws Throwable {
        log.info("[APN: Es JobActivity @{}] REST request to query job activity.", SecurityUtils.getUserId());
        Page<JobActivityDTO> jobActivities = jobService.getJobActivities(jobId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(jobActivities, "/jobs/" + jobId +"/job-activities");
        return new ResponseEntity<>(jobActivities.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/jobs/{jobId}/company-team-users")
    @ApiOperation(value = "get company project team users and am")
    public ResponseEntity<List<ICompanyTeamUser.CompanyTeamUserDTO>> getCompanyTeamUserByJobId(@PathVariable("jobId") Long jobId) {
        log.info("[APN: Job Search History @{}] REST request to get company team user by job id {}.", SecurityUtils.getUserId(), jobId);
        return ResponseEntity.ok(jobService.getCompanyTeamUserByJobId(jobId));
    }

    @GetMapping("/jobs/job-title/{jobId}")
    @ApiOperation(value = "get job title by job id")
    public ResponseEntity<String> getJobTitleByJobId(@PathVariable("jobId") Long jobId) {
        log.info("[APN: Job Search job title @{}] REST request to get job title by job id {}.", SecurityUtils.getUserId(), jobId);
        return ResponseEntity.ok(jobService.getJobTitleByJobId(jobId));
    }

    @GetMapping("/jobs/search-job-by-pteamId/{teamId}")
    @ApiOperation(value = "get job title by job id")
    public ResponseEntity<Integer> searchJobsCountByPTeamId(@PathVariable("teamId") Long teamId) {
        log.info("[APN: Job search job @{}] REST request to search job by teamId : {}", SecurityUtils.getUserId(), teamId);
        return ResponseEntity.ok(jobService.searchJobsCountByPTeamId(teamId));
    }

    @GetMapping("/jobs/search-job-sales-lead/{companyId}/{subcategory}")
    @ApiOperation(value = "get job sales lead")
    public ResponseEntity<List<SalesLeadDTO>> searchJobsSalesLeadList(@PathVariable("companyId") Long companyId, @PathVariable("subcategory") String subcategory) {
        log.info("[APN: Job search job sales lead @{}] REST request to search job sales lead : {}", SecurityUtils.getUserId(), companyId);
        return ResponseEntity.ok(jobService.searchJobSalesLead(companyId, subcategory));
    }

    @GetMapping("/jobs/search-job-history-recommend/{talentId}")
    @ApiOperation(value = "get job history recommend")
    public ResponseEntity<List<JobHistoryRecommendDTO>> searchJobHistoryRecommendList(@PathVariable("talentId") Long talentId) {
        log.info("[APN: Job search job sales lead @{}] REST request to search job history recommend", SecurityUtils.getUserId());
        return ResponseEntity.ok(jobService.searchHistoryJobRecommend(talentId));
    }

    /**
     *  Feign client
     */
    @GetMapping("/jobs/no-token/basic-info/{id}")
    public ResponseEntity<JobDTOV3> findOneBasicInfoNoToken(@PathVariable("id") Long jobId) {
        log.info("REST request to get basic info of job by id: {}", jobId);
        return ResponseEntity.ok(jobService.findOneBasicInfoNoToken(jobId));
    }

    @GetMapping("/jobs/{id}/locations")
    public ResponseEntity<String> getJobLocations(@PathVariable("id") Long jobId){
        log.info("REST request to get job locations by job id: {}", jobId);
        return ResponseEntity.ok(jobService.getJobLocationsByJobId(jobId));
    }

    @PutMapping("/jobs/sync-to-hr/{jobId}")
    public void updateJobNeedSyncToHr(@PathVariable("jobId") Long jobId) {
        jobRepository.updateJobNeedSyncToHr(jobId);
    }

    @Resource
    private GpuGrpcService gpuGrpcService;

    //apn pro使用
    @ApiOperation(value = "extract keywords")
    @PostMapping(value = "/extract-keywords", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<String> extractKeywords(@RequestBody ExtractKeywordsDTO dto) throws Throwable {
        log.info("({},{}) REST request to extract keywords:  {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), dto);
        //GpuProto.KeywordExtractorResponse keywordExtractorResponse = gpuGrpcService.extractKeywords(dto.getContent());

        //return ResponseEntity.ok().body(keywordExtractorResponse.getResponse());
        return ResponseEntity.ok(jobService.getSearchCondition(dto));
    }

    @PostMapping(value = "/brief-jobs/ids", consumes = "application/json; charset=utf-8")
    @Timed
    public ResponseEntity<List<JobBriefDTO>> getBriefJobListByIds(@RequestBody List<Long> ids) {
        log.info("[APN: Job @{}] REST request to get job brief list by id in: {}.", SecurityUtils.getUserId(), ids);
        List<JobBriefDTO> briefJobs = jobService.getBriefJobsByIds(ids);
        return ResponseEntity.ok().body(briefJobs);
    }


    @PostMapping("/get-private-job-ids")
    public Set<Long> findPrivateJobIds(@RequestBody List<Long> jobIds){
        log.info("[APN: Job @{}] REST request to get private job id .", SecurityUtils.getUserId());
        return jobService.findPrivateJobIds(jobIds);
    }

    @PostMapping("/get-job-info-by-businessId")
    public List<JobCrossSellDTOV3> getJobInfoByBusinessId(@RequestBody List<Long> businessIdList){
        log.info("[APN: Job @{}] REST request to get job business id {}", SecurityUtils.getUserId(),businessIdList);
        return jobService.getJobInfoByBusinessId(businessIdList);
    }

    @PostMapping("/check-job-permission-by-id")
    public ResponseEntity<List<JobPermissionDTO>> checkJobPermissionById(@RequestBody List<Long> jobIds){
        log.info("[APN: Job @{}] REST request to get job  id {}", SecurityUtils.getUserId(),jobIds);
        return ResponseEntity.ok().body(jobService.checkJobPermissionById(jobIds));
    }

    @PostMapping("/check-job-permission-by-china-invoicing")
    public ResponseEntity<JobPermissionDTO> checkJobPermissionByChinaInvoicing(@RequestBody Long jobId){
        log.info("[APN: Job @{}] REST request to check permission job  id {}", SecurityUtils.getUserId(),jobId);
        return ResponseEntity.ok().body(jobService.checkJobPermissionByChinaInvoicing(jobId));
    }
}
