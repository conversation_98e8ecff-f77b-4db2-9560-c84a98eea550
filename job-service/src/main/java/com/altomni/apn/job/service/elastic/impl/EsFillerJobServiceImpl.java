package com.altomni.apn.job.service.elastic.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.config.constants.EsFillerConstants;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.dict.JobJobFunctionRelation;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatus;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderUserRole;
import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.job.JobAdditionalInfo;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.AdditionalInfoDTO;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderTalentRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.enums.EnumDegreeService;
import com.altomni.apn.common.service.enums.EnumLanguageService;
import com.altomni.apn.common.service.enums.EnumUserResponsibilityService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.config.env.EsfillerMQProperties;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.agency.AgencyJobRelation;
import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.job.domain.job.JobLocation;
import com.altomni.apn.job.domain.job.SyncJobToIpgRelation;
import com.altomni.apn.job.repository.agency.AgencyJobRelationRepository;
import com.altomni.apn.job.repository.agency.JobSharingToAgencyRepository;
import com.altomni.apn.job.repository.async.AsyncRecordRepository;
import com.altomni.apn.job.repository.job.*;
import com.altomni.apn.job.service.application.ApplicationService;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.dto.address.LocationESDTO;
import com.altomni.apn.job.service.dto.agency.JobSharingToAgencyWithStatusDTO;
import com.altomni.apn.job.service.dto.folder.JobCategoryCountRequestDTO;
import com.altomni.apn.job.service.dto.job.FoldersOfPreSubmitTalents;
import com.altomni.apn.job.service.dto.job.IEsClientContact;
import com.altomni.apn.job.service.dto.job.JobEsSyncDocument;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.job.JobNoteService;
import com.altomni.apn.job.service.rabbitmq.RabbitMqService;
import com.altomni.apn.job.service.rater.RaterService;
import com.altomni.apn.job.service.redis.RedisService;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.job.web.rest.vm.JobTitleVM;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.config.constants.ElasticSearchConstants.*;
import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

/**
 * <AUTHOR>
 */
@Slf4j
@Resource
@Service
public class EsFillerJobServiceImpl implements EsFillerJobService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private HttpService httpService;

    @Resource
    private AsyncRecordRepository asyncRecordRepository;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private JobSharingToAgencyRepository jobSharingToAgencyRepository;

    @Resource
    private AgencyJobRelationRepository agencyJobRelationRepository;

    @Resource
    private JobFunctionRelationRepository jobFunctionRelationRepository;

    @Resource
    private JobNoteService jobNoteService;

    @Resource
    private UserService userService;

    @Resource
    private CompanyService companyService;

    @Resource
    private JobCompanyContactRelationRepository jobCompanyContactRelationRepository;


    @Resource
    private ApplicationService applicationService;

    @Resource
    private JobLocationRepository jobLocationRepository;

    @Resource
    private EnumLanguageService enumLanguageService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private EnumDegreeService enumDegreeService;

    @Resource
    private EnumUserResponsibilityService enumUserResponsibilityService;

    @Resource
    private RedisService redisService;

    @Resource
    private CanalService canalService;

    @Resource
    private JobFolderRelationRepository jobFolderRelationRepository;

    @Resource
    private UserJobRelationRepository userJobRelationRepository;

    @Resource(name = "jobdivaRabbitTemplate")
    private RabbitTemplate jobdivaRabbitTemplate;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    @Resource
    CachePermission cachePermission;

    private final int ERROR_CODE = 504;

    private final String ES_JOB_NOTE_TYPE = "job_note";

    public final static Set<String> USELESS_KEYS = new HashSet<>(new ArrayList<>(List.of( //除了jobType弃用之外，其他数据放入jobAdditionalInfo中的localExtendedInfo (这部分数据esfiller不接受，不会同步给es)
            "reasonForRecruitment",
            "teamComposition",
            "preferredCompanies",
            "suggestionsForProspecting",
            "recommendedApproach",
            "estimatedJobFee",
            "feeStructure",
            "contractSigningParty",
            "jobType", //no use, deprecated
            "preferredIndustry",
            "companyInfoText"
    )));

    public final static Set<String> NOT_KEEP_KEYS = new HashSet<>(new ArrayList<>(List.of( // esfiller接受，但每次都创建/更新job时取新的值，这些值是用户的输入，前端一定要传
            "department",
            "experienceYearRange",
            "salaryRange",
            "billRange",
            "preferredSkills",
            "requiredSkills",
            "summary",
            "responsibilities",
            "requirements"
    )));

    @Resource
    private SyncJobToIpgRelationRepository syncJobToIpgRelationRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    private RaterService raterService;

    private final static String ES_PUBLISHED_KEY = "published";

    private String syncUrl(Long tenantId, Long jobId) {
        return applicationProperties.getSyncUrl() + tenantId + "/job/" + jobId + "/fill_es/";
    }

    private String updateJobFolderUrl(Long tenantId) {
        return applicationProperties.getSyncUrl() + tenantId + "/jobs_folder_update";
    }

    private String updateUrl(Long tenantId, Long jobId) {
        return applicationProperties.getUpdateUrl() + tenantId + "/job/" + jobId + "/fill_es/_update";
    }

    private String updateJobStatusUrl(Long tenantId) {
        return applicationProperties.getSyncUrl() + tenantId + "/jobs_status_update";
    }

    private String commonServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent";
    }

    private String categoryCountCommonServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/get-job-count";
    }

    private String updateJobNoteUrl(Long tenantId, Long jobId) {
        return applicationProperties.getSyncUrl() + "/job_note/" + tenantId + "/job_note/" + jobId;
    }

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void syncJobStatusToEs(Long tenantId, Long jobId, JobStatus status) {
        SecurityContext context = SecurityContextHolder.getContext();
        //CompletableFuture.runAsync(() -> {
        SecurityContextHolder.setContext(context);
        JSONObject statusObject = new JSONObject();
        statusObject.put("status", status.getName());
        try {
            HttpResponse response = httpService.post(updateUrl(tenantId, jobId), JSON.toJSONString(statusObject));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerJobService @{}] update job status to EsFiller success, id: {}", SecurityUtils.getUserId(), jobId);
                asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, jobId, AsyncEnum.DATA_TYPE_JOB, Status.Available, response.getCode(), response.getBody()));
            } else {
                log.info("[APN: EsFillerJobService @{}] update job status to EsFiller error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), jobId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, jobId, AsyncEnum.DATA_TYPE_JOB, Status.Failed, response != null ? response.getCode() : null, response != null ? response.getBody() : null));
            }
        } catch (IOException e) {
            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, jobId, AsyncEnum.DATA_TYPE_JOB, Status.Failed, ERROR_CODE, "ESFiller timeout"));
            log.info("[APN: EsFillerJobService @{}] update job({}) status to EsFiller IOException", SecurityUtils.getUserId(), jobId, e);
        }
        //});
    }

    @Override
    public void syncJobStatusToEsV3(Long tenantId, Long jobId, JobStatus status) {
        SecurityContext context = SecurityContextHolder.getContext();
        SecurityContextHolder.setContext(context);
        JSONObject statusObject = new JSONObject();
        statusObject.put("ids", Collections.singletonList(jobId.toString()));
        statusObject.put("status", status.getName());
        Instant jobEsLastModifiedTime = jobRepository.getJobEsLastModifiedTime(jobId);
        statusObject.put("lastModifiedDate", DateUtil.fromInstantToUtcDateTimeWithMillisecond(jobEsLastModifiedTime));
        try {
            HttpResponse response = httpService.post(updateJobStatusUrl(tenantId), JSON.toJSONString(statusObject));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, jobId, AsyncEnum.DATA_TYPE_JOB, Status.Available, response.getCode(), response.getBody()));
            } else {
                log.info("[APN: EsFillerJobService @{}] update job status to EsFiller error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), jobId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, jobId, AsyncEnum.DATA_TYPE_JOB, Status.Failed, response != null ? response.getCode() : null, response != null ? response.getBody() : null));
            }
        } catch (IOException e) {
            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, jobId, AsyncEnum.DATA_TYPE_JOB, Status.Failed, ERROR_CODE, "ESFiller timeout"));
            log.info("[APN: EsFillerJobService @{}] update job({}) status to EsFiller IOException", SecurityUtils.getUserId(), jobId, e);
        }
    }


    @Override
    public void sendJobNoteToMq(Long tenantId, Long jobId, JobNoteDTO jobNoteDTO, Boolean deleted) {
        log.info("[EsFillerJobService: syncJobNoteToMQ @{}] jobNoteProfiles start id: {}", SecurityUtils.getUserId(), jobId);
        JSONObject jsonObject = buildJobNoteProfile(tenantId, jobId, jobNoteDTO, deleted);

        try {
            rabbitMqService.saveProfile(JSONUtil.toJsonStr(jsonObject), ES_JOB_NOTE_TYPE, 1);
            log.info("[EsFillerJobService: syncJobNoteToMQ @{}] save job Note to MQ success, id: {} for jobId ", SecurityUtils.getUserId(), jobNoteDTO.getId(), jobId);
        } catch (Exception e) {
            log.error("[EsFillerJobService: syncJobNoteToMQ @{}] save job Note to MQ error, id: {} for jobId ", SecurityUtils.getUserId(), jobNoteDTO.getId(), jobId);

            String message = "Send Job Note to ES Error" +
                             "\n\tJob ID: " + jobId +
                             "\t Job Note Id: " + jobNoteDTO.getId() +
                             "\n\tError: " +
                             "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }

    }


    private JSONObject buildJobNoteProfile(Long tenantId, Long jobId, JobNoteDTO jobNoteDTO, Boolean deleted) {
        log.info("[EsFillerJobService: syncJobNoteToMQ @{}] building Job Note for job:{}", SecurityUtils.getUserId(), jobId);

        JobEsSyncDocument.Note esNote = new JobEsSyncDocument.Note();
        esNote.setId(String.valueOf(jobNoteDTO.getId()));
        esNote.setText(HtmlUtil.cleanHtmlTag(jobNoteDTO.getNote()));
        esNote.setCreatedDate(DateUtil.fromInstantToUtcDateTime(jobNoteDTO.getCreatedDate()));
        esNote.setLastModifiedDate(DateUtil.fromInstantToUtcDateTime(jobNoteDTO.getLastModifiedDate()));
        if (jobNoteDTO.getCreatedBy() != null) {
            esNote.setResponsibility5(List.of(new JobEsSyncDocument.Responsibility(String.valueOf(jobNoteDTO.getCreatedBy().getId()), jobNoteDTO.getCreatedBy().getFullName())));
        }
        if (jobNoteDTO.getLastModifiedBy() != null) {
            esNote.setResponsibility0(List.of(new JobEsSyncDocument.Responsibility(String.valueOf(jobNoteDTO.getLastModifiedBy().getId()), jobNoteDTO.getLastModifiedBy().getFullName())));
        }
        JSONObject document = new JSONObject();
        document.put("_id", jobId);
        document.put("_tenant_id", String.valueOf(tenantId));
        document.put("_type", ES_JOB_NOTE_TYPE);
        document.put("_return_routing_key", esfillerMQProperties.getApnNormalizedJobRoutingKey());
        document.put("_source", esNote);
        document.put("_deleted", deleted);
        return document;
    }

    private JSONObject translateEs(JobV3 job) {
        //add unusedKey from jdParser
        JSONObject esDocumentJson = new JSONObject();
        com.alibaba.fastjson.JSONObject extendedJson = JSON.parseObject(job.getJobExtendedInfo());
        if (CollUtil.isNotEmpty(extendedJson)) {
            extendedJson.remove("jobFunctions");
            extendedJson.remove("clientContactName");
            extendedJson.remove("clientContactCategory");
            extendedJson.remove("clientContactEmails");
            extendedJson.remove("publicDesc");
            extendedJson.remove("preferredDegrees");
            extendedJson.remove("companyName");
            extendedJson.remove("LANGUAGES");
            extendedJson.remove("languages");
            extendedJson.remove("NECESSITY");
            extendedJson.remove("RELATIONSHIP");
            extendedJson.remove("DEGREES");
            extendedJson.remove("company_infoText");
            extendedJson.remove("companyInfoText");
            extendedJson.remove("locations");//TODO
            extendedJson.remove("location");
            extendedJson.remove("jdText");
            extendedJson.remove("uuid");
            final Object payType = extendedJson.getOrDefault("payType", null);
            if (Objects.isNull(payType)) {
                extendedJson.put("payType", "YEARLY");
            }
            final com.alibaba.fastjson.JSONObject billRange = (com.alibaba.fastjson.JSONObject) extendedJson.getOrDefault("billRange", null);
            if (Objects.nonNull(billRange)) {
                final Integer gte = (Integer) billRange.getOrDefault("gte", null);
                final Integer lte = (Integer) billRange.getOrDefault("lte", null);
                if (Objects.nonNull(gte) && Objects.nonNull(lte) && gte.compareTo(lte) > 0) {
                    extendedJson.remove("billRange");
                }
            }

            esDocumentJson.putAll(JSON.parseObject(JSON.toJSONString(extendedJson, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1))));
        }
        //set jd keys
        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getRequirements())) {
            esDocumentJson.put(JD_REQUIREMENT_TEXT, HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getRequirements()));
        }
        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getSummary())) {
            esDocumentJson.put(JD_SUMMARY_TEXT, HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getSummary()));
        }
        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getResponsibilities())) {
            esDocumentJson.put(JD_RESPONSIBILITY_TEXT, HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getResponsibilities()));
        }
        //TODO: JOBV3 Deprecate
//        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getPublicDesc())) {
//            esDocumentJson.put(ESFILLER_JD_TEXT, HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getPublicDesc()));
//        }

        if (ObjectUtil.isNotEmpty(job.getCode())) {
            esDocumentJson.put("code", job.getCode());
        }
        esDocumentJson.put("title", job.getTitle());
        if (!JobType.PAY_ROLL.equals(this.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId()))) {
            esDocumentJson.put("status", job.getStatus().getName());
        } else {
            esDocumentJson.remove("status");
        }

        if (ObjectUtil.isNotEmpty(job.getStartDate())) {
            esDocumentJson.put("startDate", DateUtil.fromInstantToDate(job.getStartDate()));
        }
        if (ObjectUtil.isNotEmpty(job.getEndDate())) {
            esDocumentJson.put("endDate", DateUtil.fromInstantToDate(job.getEndDate()));
        }
        esDocumentJson.put("postingTime", job.getPostingTime());
        esDocumentJson.put("createdDate", job.getCreatedDate());
        esDocumentJson.put("lastModifiedDate", job.getLastModifiedDate());
        TalentRecruitmentProcessVO talentRecruitmentProcessVO = applicationService.getTalentRecruitmentProcessLastByJobId(job.getId()).getBody();
        if (ObjectUtil.isNotNull(talentRecruitmentProcessVO)) {
            esDocumentJson.put("lastActivityTime", talentRecruitmentProcessVO.getCreatedDate());
        }
        String lastSubmitInterviewTime = applicationService.getTalentRecruitmentProcessLastInterviewDateTimeByJobId(job.getId()).getBody();
        if(StringUtils.isNotEmpty(lastSubmitInterviewTime)) {
            esDocumentJson.put("lastSubmitInterviewTime", lastSubmitInterviewTime);
        }
        //maxSubmissions
        if (ObjectUtil.isNotEmpty(job.getMaxSubmissions())) {
            esDocumentJson.put("maxSubmissions", job.getMaxSubmissions());
        }
        //openings
        if (ObjectUtil.isNotEmpty(job.getOpenings())) {
            esDocumentJson.put("openings", job.getOpenings());
        }
        //locations
        List<JobLocation> locations = jobLocationRepository.findAllByJobIdAndOriginalLocIsNotNull(job.getId());
        if (CollUtil.isNotEmpty(locations)) {
            esDocumentJson.put("locations", locations.stream()
                    .map(s -> {
                        JSONObject jsonObject = JSONUtil.toBean(s.getOriginalLoc(), JSONObject.class);
                        jsonObject.put("id", s.getId());
                        return jsonObject;
                    }).collect(Collectors.toList()));
        }
        //company
        CompanyDTO company = companyService.getCompany(job.getCompanyId()).getBody();
        if (ObjectUtil.isNotEmpty(company)) {
            esDocumentJson.put("companyId", ObjectUtil.toString(company.getId()));
            esDocumentJson.put("companyName", company.getName());
            if (CollUtil.isNotEmpty(company.getIndustries())) {
                esDocumentJson.put("industries", company.getIndustries().stream().map(i -> Integer.parseInt(i.getEnumId())).collect(Collectors.toList()));
            }
            if (ObjectUtil.isNotEmpty(company.getLogo())) {
                esDocumentJson.put("logo", company.getLogo());
            }
        }
        //List<Long> userIds = userFavoriteJobRepository.findAllByJobId(job.getId()).stream().map(UserFavoriteJob::getUserId).distinct().collect(Collectors.toList());
        final Set<Long> folderIds = jobFolderRelationRepository.findFolderIdsByJobId(job.getId());
        if (ObjectUtil.isNotEmpty(folderIds)) {
            esDocumentJson.put("folders", folderIds.stream().map(StrUtil::toString).collect(Collectors.toList()));
        }
        if (Objects.nonNull(job.getRecruitmentProcessId())) {
            esDocumentJson.put("recruitmentProcessId", job.getRecruitmentProcessId().toString());

            esDocumentJson.put("type", this.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId()));

        }
        // TODO ES does not support priority field currently
//        if (Objects.nonNull(job.getEnumPriorityId())){
//            esDocumentJson.put("priority", job.getEnumPriorityId());
//        }

        //assignedUsers to USER_RESPONSIBILITY
        addUserResponsibilityAttributes(esDocumentJson, job);
        //currency
        if (ObjectUtil.isNotEmpty(job.getCurrency())) {
            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(job.getCurrency());
            esDocumentJson.put("currency", enumCurrency.getName());
            esDocumentJson.put("currencyUSDExchangeRate", enumCurrency.getFromUsdRate());
        }

        //business dict value translate
        if (ObjectUtil.isNotEmpty(job.getJobFunctions())) {
            esDocumentJson.put("jobFunctions", job.getJobFunctions().stream().map(c -> Long.valueOf(c.getEnumId())).toList());
        }

        if (ObjectUtil.isNotEmpty(job.getPreferredDegrees())) {
            esDocumentJson.put("preferredDegreeLevels", enumDegreeService.transferDegreesByIds(EnumRelationDTO.convert(job.getPreferredDegrees())));
        }
        if (ObjectUtil.isNotEmpty(job.getMinimumDegreeId())) {
            List<String> minimumDegreeList = enumDegreeService.transferDegreesByIds(Collections.singletonList(String.valueOf(job.getMinimumDegreeId())));
            if (ObjectUtil.isNotEmpty(minimumDegreeList)) {
                minimumDegreeList.forEach(s -> esDocumentJson.put("minimumDegreeLevel", s));
            }
        }

        //requiredLanguages
        if (ObjectUtil.isNotEmpty(job.getRequiredLanguages())) {
            esDocumentJson.put("requiredLanguages", enumLanguageService.transferLanguagesByIds(EnumRelationDTO.convert(job.getRequiredLanguages())));
        }
        //preferredLanguages
        if (ObjectUtil.isNotEmpty(job.getPreferredLanguages())) {
            esDocumentJson.put("preferredLanguages", enumLanguageService.transferLanguagesByIds(EnumRelationDTO.convert(job.getPreferredLanguages())));
        }
        addIpgAttributes(esDocumentJson, job);
        return esDocumentJson;
    }

    /**
     * @param job
     * @param flag true时去掉 "folders", "foldersOfPreSubmitTalents" 加入 "jobId"
     * @return
     */
    private JSONObject buildEsSource(JobV3 job, boolean flag) {
        log.info("[EsFillerJobService: syncJobToMQ @{}] buildESSource: jobId:{}, lastModifiedDate:{}", SecurityUtils.getUserId(), job.getId(), job.getLastModifiedDate());
        JSONObject source = new JSONObject();
        //获取通用数据
        commonBuildEsSource(job, source);
        if (!flag) {
            // SET "folders": ["string"]
            final Set<Long> folderIds = jobFolderRelationRepository.findFolderIdsByJobId(job.getId());
            if (ObjectUtil.isNotEmpty(folderIds)) {
                source.put("folders",folderIds.stream().map(StrUtil::toString).collect(Collectors.toList()));
            }
        }

        if (!flag) {
            // SET "lastActivityTime": "string"
            TalentRecruitmentProcessVO talentRecruitmentProcessVO = applicationService.getTalentRecruitmentProcessLastByJobId(job.getId()).getBody();
            if (ObjectUtil.isNotNull(talentRecruitmentProcessVO)) {
                source.put("lastActivityTime",DateUtil.fromInstantToUtcDateTime(talentRecruitmentProcessVO.getCreatedDate()));
            }
            String lastSubmitInterviewTime = applicationService.getTalentRecruitmentProcessLastInterviewDateTimeByJobId(job.getId()).getBody();
            if(StringUtils.isNotEmpty(lastSubmitInterviewTime)) {
                source.put("lastSubmitInterviewTime", lastSubmitInterviewTime);
            }
        }

        if (!flag) {
            //setRelateJobFolderInfo
            setRelateJobFolder(job, source);
        }
        return source;
    }

    /**
     * @param jobId job id
     * @param jobStatus job status
     * @param jobSharingStr job sharing to agency info in json string
     * @param flag true时去掉 "folders", "foldersOfPreSubmitTalents" 加入 "jobId"
     * @return
     */
    private JSONObject buildEsSourceForJobSharingToAgency(Long jobId, JobStatus jobStatus, Long recruitmentProcessId, Boolean companyNameVisible, String jobSharingStr, Long companyId, String companyName, Instant jobCreatedDate, Long permissionUserId, Long permissionTeamId, boolean flag) {
        log.info("[EsFillerJobService: syncJobToAgencyMQ @{}] buildEsSourceForJobSharingToAgency: jobId:{}, lastModifiedDate:", SecurityUtils.getUserId(), jobId);

        List<AgencyJobRelation> sharedAgencies = agencyJobRelationRepository.findAllByJobIdAndShareStatus(jobId, JobShareStatus.ACTIVE);
        Set<String> sharedAgencyIds = new HashSet<>();
        if (ObjectUtil.isNotEmpty(sharedAgencies)) {
            sharedAgencyIds = sharedAgencies.stream().map(AgencyJobRelation::getAgencyId).map(String::valueOf).collect(Collectors.toSet());
        }

        JSONObject source = new JSONObject();
        //获取通用数据
        commonBuildEsSource2(jobStatus, recruitmentProcessId, jobSharingStr, companyNameVisible, source, sharedAgencyIds, companyId, companyName, jobCreatedDate, permissionUserId, permissionTeamId);

        //TODO: agencyJob, too much info, no need
//        if (!flag) {
//            // SET "folders": ["string"]
//            final Set<Long> folderIds = jobFolderRelationRepository.findFolderIdsByJobId(job.getId());
//            if (ObjectUtil.isNotEmpty(folderIds)) {
//                source.put("folders",folderIds.stream().map(StrUtil::toString).collect(Collectors.toList()));
//            }
//        }

        //TODO: agencyJob, too much info, no need
//        if (!flag) {
//            // SET "lastActivityTime": "string"
//            TalentRecruitmentProcessVO talentRecruitmentProcessVO = applicationService.getTalentRecruitmentProcessLastByJobId(job.getId()).getBody();
//            if (ObjectUtil.isNotNull(talentRecruitmentProcessVO)) {
//                source.put("lastActivityTime",DateUtil.fromInstantToUtcDateTime(talentRecruitmentProcessVO.getCreatedDate()));
//            }
//            String lastSubmitInterviewTime = applicationService.getTalentRecruitmentProcessLastInterviewDateTimeByJobId(job.getId()).getBody();
//            if(StringUtils.isNotEmpty(lastSubmitInterviewTime)) {
//                source.put("lastSubmitInterviewTime", lastSubmitInterviewTime);
//            }
//        }

        //TODO: agencyJob, too much info, no need
//        if (!flag) {
//            //setRelateJobFolderInfo
//            setRelateJobFolder(job, source);
//        }

        return source;
    }

    private void commonBuildEsSource(JobV3 job, JSONObject source) {

        // SET "sharedAgencies": ["string"]
        List<AgencyJobRelation> sharedAgencies = agencyJobRelationRepository.findAllByJobIdAndShareStatus(job.getId(), JobShareStatus.ACTIVE);
        if (ObjectUtil.isNotEmpty(sharedAgencies)) {
            Set<String> sharedAgencyIds = sharedAgencies.stream().map(AgencyJobRelation::getAgencyId).map(String::valueOf).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(sharedAgencyIds)) {
                source.put("sharedAgencies", sharedAgencyIds);
            }
        }

        // SET "allowRemote": boolean
        if (Objects.nonNull(job.getFlexibleLocation())) {
            source.put("allowRemote", job.getFlexibleLocation());
        }

        // SET "allowRemote": boolean
        if (Objects.nonNull(job.getSalesLeadId())) {
            source.put("salesLeadId", job.getSalesLeadId());
        }

        // SET "affiliations": ["string"] see this.addUserResponsibilityAttributes()

        // SET "billRange": {"lte": 0, "gte": 0} see this.setJobAdditionalInfo()

        // SET clientContacts
        List<IEsClientContact> clientContacts = jobCompanyContactRelationRepository.findClientContactsByJobId(job.getId());
        if (CollectionUtils.isNotEmpty(clientContacts)) {
            source.put("clientContacts", clientContacts
                    .stream()
                    .map(c -> new JobEsSyncDocument.ClientContact(c.getClientContactId().toString(), c.getTalentId().toString()))
                    .collect(Collectors.toList()));
        }

        // SET "code": "string"
        if (StringUtils.isNotEmpty(job.getCode())) {
            source.put("code", job.getCode());
        }

        /**
         * SET
         * "companyName": "string",
         * "companyId": "string",
         * "logo": "string",
         * "industries": ["string"]
         */
        CompanyDTO company = companyService.getCompany(job.getCompanyId()).getBody();
        if (ObjectUtil.isNotEmpty(company)) {
            source.put("companyId", ObjectUtil.toString(company.getId()));
            source.put("companyName", company.getName());
            if (CollUtil.isNotEmpty(company.getIndustries())) {
                //final Map<Long, String> industriesMap = enumIndustryService.getIndustriesMap();
                source.put("industries", company.getIndustries()
                        .stream()
                        .map(i -> Integer.valueOf(i.getEnumId()))
                        .collect(Collectors.toList()));
            }
            /**
             * 用于ipg官网展示
             */
            if (ObjectUtil.isNotEmpty(company.getLogo())) {
                source.put("logo", company.getLogo());
            }
        }

        // SET "createdDate": "string"
        source.put("createdDate", DateUtil.fromInstantToUtcDateTime(job.getCreatedDate()));

        //jobSnap时需要去掉计算hash 计算完在加回来
        // SET "lastModifiedDate": "string" : must be the max last modified date from all job and job related table
        Instant lastModifiedDate = getJobLastModifiedDate(job);
        if (lastModifiedDate != null) {
            source.put("lastModifiedDate", DateUtil.fromInstantToUtcDateTimeWithMillisecond(lastModifiedDate));
        }

        // SET "currency": "string", "currencyUSDExchangeRate": float
        if (ObjectUtil.isNotEmpty(job.getCurrency())) {
            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(job.getCurrency());
            source.put("currency", enumCurrency.getName());
            source.put("currencyUSDExchangeRate", enumCurrency.getFromUsdRate());
        }

        // SET "department": "string" see this.setJobAdditionalInfo()

        // SET "endDate": "string"
        if (ObjectUtil.isNotEmpty(job.getEndDate())) {
            source.put("endDate", DateUtil.fromInstantToDate(job.getEndDate()));
        }

        // SET "experienceYearRange": {"lte": 0, "gte": 0}
        JobAdditionalInfo jobAdditionalInfo = job.getJobAdditionalInfo();
        if (ObjectUtil.isNotEmpty(jobAdditionalInfo)) {
            String extendedInfo = jobAdditionalInfo.getExtendedInfo();
            if (ObjectUtil.isNotEmpty(extendedInfo)) {
                AdditionalInfoDTO additionalInfoDTO = JsonUtil.fromJson(JSONUtil.toJsonStr(extendedInfo), AdditionalInfoDTO.class);
                RangeDTO experienceYearRange = additionalInfoDTO.getExperienceYearRange();
                if (ObjectUtil.isNotEmpty(experienceYearRange)) {
                    source.put("experienceYearRange", experienceYearRange);
                }
            }
        }


        // SET "industries": ["string"] see company setting above

        // SET "jobFunctions": ["string"]
        if (ObjectUtil.isNotEmpty(job.getJobFunctions())) {
            source.put("jobFunctions", job.getJobFunctions().stream()
                    .map(f -> Long.valueOf(f.getEnumId()))
                    .toList());
        }


        // SET locations
        List<JobLocation> locations = jobLocationRepository.findAllByJobIdAndOriginalLocIsNotNull(job.getId());
        if (CollUtil.isNotEmpty(locations)) {
            source.put("locations", locations.stream()
                    .map(s -> {
                        JobEsSyncDocument.Location location = JSONUtil.toBean(s.getOriginalLoc(), JobEsSyncDocument.Location.class);
                        location.setId(s.getId().toString());
                        return location;
                    })
                    .collect(Collectors.toList()));
        }
        //SET maxSubmissions
        if (ObjectUtil.isNotEmpty(job.getMaxSubmissions())) {
            source.put("maxSubmissions", job.getMaxSubmissions());
        }
        // SET "minimumDegreeLevel": "string"
        final Map<Long, String> degreeMap = enumDegreeService.getDegreeMap();
        if (ObjectUtil.isNotEmpty(job.getMinimumDegreeId())) {
            source.put("minimumDegreeLevel", degreeMap.get(job.getMinimumDegreeId()));
        }
        // SET "openings": 0
        source.put("openings", job.getOpenings());

        // SET "recruitmentProcessId": "string"
        if (Objects.nonNull(job.getRecruitmentProcessId())) {
            source.put("recruitmentProcessId", job.getRecruitmentProcessId().toString());

        }

        // SET "requiredLanguages": ["string"]
        final Map<Long, String> languageMap = enumLanguageService.getLanguageMap();
        if (ObjectUtil.isNotEmpty(job.getRequiredLanguages())) {
            source.put("requiredLanguages", job.getRequiredLanguages()
                    .stream()
                    .map(l -> languageMap.get(Long.valueOf(l.getEnumId())))
                    .collect(Collectors.toList()));
        }

        // SET "preferredLanguages": ["string"]
        if (ObjectUtil.isNotEmpty(job.getPreferredLanguages())) {
            source.put("preferredLanguages", job.getPreferredLanguages()
                    .stream()
                    .map(l -> languageMap.get(Long.valueOf(l.getEnumId())))
                    .collect(Collectors.toList()));
        }

        // SET "preferredDegreeLevels": ["string"]
        if (ObjectUtil.isNotEmpty(job.getPreferredDegrees())) {
            source.put("preferredDegreeLevels", job.getPreferredDegrees().stream().map(d -> degreeMap.get(Long.valueOf(d.getEnumId()))).collect(Collectors.toList()));
        }
        // SET requiredSkills see this.setJobAdditionalInfo()

        // SET "preferredCurrency": "string" see this.setJobAdditionalInfo()

        // SET preferredSkills see this.setJobAdditionalInfo()

        // SET "payTimes": 0 TODO

        // SET "payType": "string" see this.setJobAdditionalInfo()

        // SET "priority": 0 TODO ES does not support priority field currently
        if (Objects.nonNull(job.getEnumPriorityId())) {
            source.put("priority", job.getEnumPriorityId().byteValue());
        }

        // SET "published": "string"
        if (JobType.PAY_ROLL != this.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId())) {
            SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
            if (oldSyncJobToIpgRelation == null || oldSyncJobToIpgRelation.getIpgJobStatus() == JobStatus.CLOSED || oldSyncJobToIpgRelation.getIpgJobStatus() == JobStatus.NO_PUBLISHED) {
                source.put("published", false);
            } else {
                source.put("published", true);
                source.put("tenantWebsitePostingTime", DateUtil.fromInstantToUtcDateTime(oldSyncJobToIpgRelation.getLastModifiedDate()));
            }
        }

        // SET "postingTime": "string"
        source.put("postingTime", DateUtil.fromInstantToUtcDateTime(job.getPostingTime()));
        // 最后一open job的时间
        source.put("openTime", DateUtil.fromInstantToUtcDateTime(job.getOpenTime()));


        // SET "salaryRange": {"lte": 0, "gte": 0} see this.setJobAdditionalInfo()

        // SET "sponsorWorkAuths": ["string"] see this.setJobAdditionalInfo()

        // SET "startDate": "string"
        if (ObjectUtil.isNotEmpty(job.getStartDate())) {
            source.put("startDate", DateUtil.fromInstantToDate(job.getStartDate()));
        }

        // SET "status": "00:OPEN" TODO
        if (!JobType.PAY_ROLL.equals(this.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId()))) {
            source.put("status", job.getStatus().getName());
        }


        if (ObjectUtil.isNotEmpty(job.getJobAdditionalInfo())) {
            // SET "requirementText": "string"
            String requirements = null;
            if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getRequirements())) {
                requirements = HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getRequirements());
                source.put("requirementText", requirements);
            }

            // SET "responsibilityText": "string"
            String responsibilities = null;
            if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getResponsibilities())) {
                responsibilities = HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getResponsibilities());
                source.put("responsibilityText", responsibilities);
            }

            // SET "summaryText": "string"
            String summary = null;
            if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getSummary())) {
                summary = HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getSummary());
                source.put("summaryText", summary);
            }

            // SET "text": "string"
            String text = Stream.of(requirements, responsibilities, summary)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("\n"));
            if (StringUtils.isNotBlank(text)) {
                source.put("text", text.trim());
            }
        }

        //ST "job_note": "array"
        this.setJobNote(source, job);


        // SET "title": "string"
        source.put("title", job.getTitle());

        /**
         SET
         "responsibility(0-19)": [
         {
         "id": "string",
         "name": "string"
         }
         ]
         */
        this.addUserResponsibilityAttributes(source, job);

        this.setJobAdditionalInfo(job, source);


        //favoriteUsers todo => folders
//        List<Long> userIds = userFavoriteJobRepository.findAllByJobId(job.getId()).stream().map(UserFavoriteJob::getUserId).distinct().collect(Collectors.toList());
//        if (ObjectUtil.isNotEmpty(userIds)) {
//            esDocumentJson.put("favoriteUserIds", userIds.stream().map(StrUtil::toString).collect(Collectors.toList()));
//        }
        //List<Long> userIds = userFavoriteJobRepository.findAllByJobId(job.getId()).stream().map(UserFavoriteJob::getUserId).distinct().collect(Collectors.toList());
    }

    private void commonBuildEsSource2(JobStatus jobStatus, Long recruitmentProcessId, String jobSharingStr, Boolean companyNameVisible, JSONObject source, Set<String> sharedAgencyIds, Long companyId, String companyName, Instant jobCreatedDate, Long permissionUserId, Long permissionTeamId) {
        JSONObject jobSharingJson = JSONUtil.parseObj(jobSharingStr);

        // SET "title": "string"
        if (jobSharingJson.containsKey("title")) {
            source.put("title", jobSharingJson.getStr("title"));
        }

        // SET "allowRemote": boolean
        if (jobSharingJson.containsKey("flexibleLocation")) {
            source.put("allowRemote", jobSharingJson.getBool("flexibleLocation"));
        }

        // SET "sharedAgencies": ["string"]
        if (CollectionUtils.isNotEmpty(sharedAgencyIds)) {
            source.put("sharedAgencies", sharedAgencyIds);
        }

        // SET "createdDate": "string"
        source.put("createdDate", DateUtil.fromInstantToUtcDateTime(jobCreatedDate));


        // SET "affiliations": ["string"] see this.addUserResponsibilityAttributes()
        List<String> affiliations = new ArrayList<>();
        //set affiliations for permission
        if (ObjectUtil.isNotNull(permissionUserId)) {
            affiliations.add(EsFillerConstants.ES_PUSER + permissionUserId);
        }
        if (ObjectUtil.isNotNull(permissionTeamId)) {
            affiliations.add(EsFillerConstants.ES_PTEAM + permissionTeamId);
        } else {
            affiliations.add("all");
        }
        source.put("affiliations", affiliations);

        // SET "billRange": {"lte": 0, "gte": 0} see this.setJobAdditionalInfo()

        //NO need for jobSharingToAgencyInfo
//        // SET clientContacts
//        List<IEsClientContact> clientContacts = jobCompanyContactRelationRepository.findClientContactsByJobId(job.getId());
//        if (CollectionUtils.isNotEmpty(clientContacts)) {
//            source.put("clientContacts", clientContacts
//                    .stream()
//                    .map(c -> new JobEsSyncDocument.ClientContact(c.getClientContactId().toString(), c.getTalentId().toString()))
//                    .collect(Collectors.toList()));
//        }


        //TODO: agencyJob, not found
//        // SET "code": "string"
//        if (StringUtils.isNotEmpty(job.getCode())) {
//            source.put("code", job.getCode());
//        }


        /**
         * SET
         * "companyName": "string",
         * "companyId": "string",
         * "logo": "string",
         * "industries": ["string"]
         */
//        if (jobSharingJson.containsKey("company")) { //TODO: agencyJob, double check
//            JSONObject companyObj = jobSharingJson.getJSONObject("company");
//            if (companyObj.containsKey("id")) {
//                Long companyId = companyObj.getLong("id");
//
//                CompanyDTO company = companyService.getCompany(companyId).getBody();
//                if (ObjectUtil.isNotEmpty(company)) {
//                    source.put("companyId", ObjectUtil.toString(company.getId()));
//                    source.put("companyName", company.getName());
//
//                    if (CollUtil.isNotEmpty(company.getIndustries())) {
//                        final Map<Long, String> industriesMap = enumIndustryService.getIndustriesMap();
//                        source.put("industries", company.getIndustries()
//                                .stream()
//                                .map(i -> industriesMap.get(Long.valueOf(i.getEnumId())))
//                                .collect(Collectors.toList()));
//                    }
//                    /**
//                     * 用于ipg官网展示
//                     */
//                    if (ObjectUtil.isNotEmpty(company.getLogo())) {
//                        source.put("logo", company.getLogo());
//                    }
//                }
//            }
//        }

        source.put("companyId", ObjectUtil.toString(companyId));
        source.put("companyName", companyName);

//        if (BooleanUtils.isTrue(companyNameVisible) && jobSharingJson.containsKey("companyId")) { //TODO: agencyJob, double check
//            Long companyId = jobSharingJson.getLong("companyId");
//
//            CompanyDTO company = companyService.getCompany(companyId).getBody();
//            if (ObjectUtil.isNotEmpty(company)) {
//                source.put("companyId", ObjectUtil.toString(company.getId()));
//                source.put("companyName", company.getName());
//            }
//        }


        //TODO: agencyJob, current no need
//        // SET "createdDate": "string"
//        source.put("createdDate", DateUtil.fromInstantToUtcDateTime(job.getCreatedDate()));

        //TODO: agencyJob, current no need
//        //jobSnap时需要去掉计算hash 计算完在加回来
//        // SET "lastModifiedDate": "string" : must be the max last modified date from all job and job related table
//        Instant lastModifiedDate = getJobLastModifiedDate(job);
//        if (lastModifiedDate != null) {
//            source.put("lastModifiedDate", DateUtil.fromInstantToUtcDateTimeWithMillisecond(lastModifiedDate));
//        }

        // SET "currency": "string", "currencyUSDExchangeRate": float
        if (jobSharingJson.containsKey("currency")) {
            JSONObject currencyObj = jobSharingJson.getJSONObject("currency");
            if (currencyObj.containsKey("enumId")) {
                Integer enumId = currencyObj.getInt("enumId");
                EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(enumId);
                source.put("currency", enumCurrency.getName());
                source.put("currencyUSDExchangeRate", enumCurrency.getFromUsdRate());
            }
        }

        // SET "department": "string" see this.setJobAdditionalInfo()

        //TODO: agencyJob, double check
//        // SET "endDate": "string"
//        if (ObjectUtil.isNotEmpty(job.getEndDate())) {
//            source.put("endDate", DateUtil.fromInstantToDate(job.getEndDate()));
//        }

        // SET "experienceYearRange": {"lte": 0, "gte": 0}
        if (jobSharingJson.containsKey("experienceYearRange")) {
            JSONObject experienceYearRangeObj = jobSharingJson.getJSONObject("experienceYearRange");
            JSONObject range = new JSONObject();
            if (experienceYearRangeObj.containsKey("gte")) {
                range.put("gte", experienceYearRangeObj.getInt("gte"));
            }
            if (experienceYearRangeObj.containsKey("lte")) {
                range.put("lte", experienceYearRangeObj.getInt("lte"));
            }
            source.put("experienceYearRange", range);
        }

        // SET "industries": ["string"] see company setting above

        // SET "jobFunctions": ["string"]
        if (jobSharingJson.containsKey("jobFunctions")) {
            JSONArray jobFunctionsArray = jobSharingJson.getJSONArray("jobFunctions");
            List<Long> enumIds = new ArrayList<>();
            for (int i = 0; i < jobFunctionsArray.size(); i++) {
                JSONObject jobFunctionObj = jobFunctionsArray.getJSONObject(i);
                if (jobFunctionObj.containsKey("enumId")) {
                    Long enumId = jobFunctionObj.getLong("enumId");
                    enumIds.add(enumId);
                }
            }
            source.put("jobFunctions", enumIds);
        }

        // SET locations
        if (jobSharingJson.containsKey("locations")) {
            JSONArray jobLocationsArray = jobSharingJson.getJSONArray("locations");
            for (int i = 0; i < jobLocationsArray.size(); i++) {
                JSONObject jobLocationObj = jobLocationsArray.getJSONObject(i);
                JobEsSyncDocument.Location location = JSONUtil.toBean(jobLocationObj, JobEsSyncDocument.Location.class);
                location.setId(null);
            }
            source.put("locations", jobLocationsArray);
        }

        //TODO: agencyJob, double check
//        //SET maxSubmissions
//        if (ObjectUtil.isNotEmpty(job.getMaxSubmissions())) {
//            source.put("maxSubmissions", job.getMaxSubmissions());
//        }

        // SET "minimumDegreeLevel": "string"
        final Map<Long, String> degreeMap = enumDegreeService.getDegreeMap();
        if (jobSharingJson.containsKey("minimumDegreeLevel")) {
            JSONObject minimumDegreeLevelObj = jobSharingJson.getJSONObject("minimumDegreeLevel");
            if (minimumDegreeLevelObj.containsKey("enumId")) {
                Long enumId = minimumDegreeLevelObj.getLong("enumId");
                source.put("minimumDegreeLevel", degreeMap.get(enumId));
            }
        }

        // SET "openings": 0
        if (jobSharingJson.containsKey("openings")) {
            source.put("openings", jobSharingJson.getInt("openings"));
        }

        // SET "recruitmentProcessId": "string"
//        String recruitmentProcessId = null;
//        if (jobSharingJson.containsKey("recruitmentProcess")) {
//            JSONObject recruitmentProcessObj = jobSharingJson.getJSONObject("recruitmentProcess");
//            if (recruitmentProcessObj.containsKey("id")) {
//                recruitmentProcessId = recruitmentProcessObj.getStr("id");
//                source.put("recruitmentProcessId", recruitmentProcessId);
//            }
//        }

        source.put("recruitmentProcessId", String.valueOf(recruitmentProcessId));


        // SET "requiredLanguages": ["string"]
        final Map<Long, String> languageMap = enumLanguageService.getLanguageMap();
        if (jobSharingJson.containsKey("requiredLanguages")) {
            List<String> requiredLanguages = new ArrayList<>();

            JSONArray requiredLanguagesArray = jobSharingJson.getJSONArray("requiredLanguages");
            for (int i = 0; i < requiredLanguagesArray.size(); i++) {
                JSONObject requiredLanguageObj = requiredLanguagesArray.getJSONObject(i);
                if (requiredLanguageObj.containsKey("enumId")) {
                    Long enumId = requiredLanguageObj.getLong("enumId");
                    requiredLanguages.add(languageMap.get(enumId));
                }
            }

            source.put("requiredLanguages", requiredLanguages);
        }


        // SET "preferredLanguages": ["string"]
        if (jobSharingJson.containsKey("preferredLanguages")) {
            List<String> preferredLanguages = new ArrayList<>();

            JSONArray preferredLanguagesArray = jobSharingJson.getJSONArray("preferredLanguages");
            for (int i = 0; i < preferredLanguagesArray.size(); i++) {
                JSONObject preferredLanguageObj = preferredLanguagesArray.getJSONObject(i);
                if (preferredLanguageObj.containsKey("enumId")) {
                    Long enumId = preferredLanguageObj.getLong("enumId");
                    preferredLanguages.add(languageMap.get(enumId));
                }
            }

            source.put("preferredLanguages", preferredLanguages);
        }

        //TODO: agencyJob, double check, not found using
//        // SET "preferredDegreeLevels": ["string"]
//        if (ObjectUtil.isNotEmpty(job.getPreferredDegrees())) {
//            source.put("preferredDegreeLevels", job.getPreferredDegrees().stream().map(d -> degreeMap.get(Long.valueOf(d.getEnumId()))).collect(Collectors.toList()));
//        }

        // SET requiredSkills see this.setJobAdditionalInfo()

        // SET "preferredCurrency": "string" see this.setJobAdditionalInfo()

        // SET preferredSkills see this.setJobAdditionalInfo()

        // SET "payTimes": 0 TODO

        // SET "payType": "string" see this.setJobAdditionalInfo()

        // SET "priority": 0 TODO ES does not support priority field currently
        if (jobSharingJson.containsKey("priority")) {
            JSONObject priorityObj = jobSharingJson.getJSONObject("priority");
            if (priorityObj.containsKey("enumId")) {
                Long enumId = priorityObj.getLong("enumId");
                source.put("priority", enumId.byteValue());
            }
        }

        //TODO: agencyJob, no need
//        // SET "published": "string"
//        if (JobType.PAY_ROLL != this.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId())) {
//            SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
//            if (oldSyncJobToIpgRelation == null || oldSyncJobToIpgRelation.getIpgJobStatus() == JobStatus.CLOSED || oldSyncJobToIpgRelation.getIpgJobStatus() == JobStatus.NO_PUBLISHED) {
//                source.put("published", false);
//            } else {
//                source.put("published", true);
//            }
//        }

        //TODO: agencyJob, double check, not found using
//        // SET "postingTime": "string"
//        source.put("postingTime", DateUtil.fromInstantToUtcDateTime(job.getPostingTime()));

        // SET "salaryRange": {"lte": 0, "gte": 0} see this.setJobAdditionalInfo()

        // SET "sponsorWorkAuths": ["string"] see this.setJobAdditionalInfo()

        // SET "startDate": "string"
        if (jobSharingJson.containsKey("startDate")) {
            String startDateStr = jobSharingJson.getStr("startDate");
//            Instant startDate = Instant.parse(startDateStr);
//            source.put("startDate", DateUtil.fromInstantToDate(startDate));
            source.put("startDate", startDateStr);
        }


        //TODO: agencyJob, double check!!!! and how to sync job status? and sharingToAgency!

        // SET "status": "00:OPEN"
//        if (StringUtils.isNotBlank(recruitmentProcessId) && !JobType.PAY_ROLL.equals(this.getJobTypeByRecruitmentProcessId(Long.valueOf(recruitmentProcessId)))) {
//            source.put("status", jobStatus);
//        }
        if (!JobType.PAY_ROLL.equals(this.getJobTypeByRecruitmentProcessId(recruitmentProcessId))) {
            source.put("status", jobStatus);
        }

        //TODO: agencyJob, double check
//        //ST "job_note": "array"
//        this.setJobNote(source, job);

        /**
         SET "responsibility(0-19)": [
         {
         "id": "string",
         "name": "string"
         }
         ]
         */
        //TODO: agencyJob, double check, too much info, no need
//        this.addUserResponsibilityAttributes(source, job);


        /*
        SET job additional info
         */

//        this.setJobAdditionalInfo(job, source);

        // SET "requirementText": "string"
        String requirementsStr = null;
        if (jobSharingJson.containsKey("requirements")) {
            requirementsStr = HtmlUtil.cleanHtmlTag(jobSharingJson.getStr("requirements"));
            source.put("requirementText", requirementsStr);
        }

        // SET "responsibilityText": "string"
        String responsibilitiesStr = null;
        if (jobSharingJson.containsKey("responsibilities")) {
            responsibilitiesStr = HtmlUtil.cleanHtmlTag(jobSharingJson.getStr("responsibilities"));
            source.put("responsibilityText", responsibilitiesStr);
        }

        // SET "summaryText": "string"
        String summaryStr = null;
        if (jobSharingJson.containsKey("summary")) {
            summaryStr = HtmlUtil.cleanHtmlTag(jobSharingJson.getStr("summary"));
            source.put("summaryText", summaryStr);
        }

        // SET "text": "string"
        String text = Stream.of(requirementsStr, responsibilitiesStr, summaryStr)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("\n"));
        if (StringUtils.isNotBlank(text)) {
            source.put("text", text.trim());
        }


        // SET "payType": "string"
        if (jobSharingJson.containsKey("payType")) {
            source.put("payType", jobSharingJson.getStr("payType"));
        } else {
            source.put("payType", RateUnitType.YEARLY.toString());
        }

        // SET "billRange": {"lte": 0, "gte": 0}
        if (jobSharingJson.containsKey("billRange")) {
            JSONObject billRangeObj = jobSharingJson.getJSONObject("billRange");
            final BigDecimal gte = billRangeObj.containsKey("gte") ? billRangeObj.getBigDecimal("gte") : null;
            final BigDecimal lte = billRangeObj.containsKey("lte") ? billRangeObj.getBigDecimal("lte") : null;
            if (Objects.isNull(gte) || Objects.isNull(lte) || gte.compareTo(lte) <= 0) {
                source.put("billRange", new RangeDTO(gte, lte));
            }
        }

        // SET "salaryRange": {"lte": 0, "gte": 0}
        if (jobSharingJson.containsKey("salaryRange")) {
            JSONObject salaryRangeObj = jobSharingJson.getJSONObject("salaryRange");
            final BigDecimal gte = salaryRangeObj.containsKey("gte") ? salaryRangeObj.getBigDecimal("gte") : null;
            final BigDecimal lte = salaryRangeObj.containsKey("lte") ? salaryRangeObj.getBigDecimal("lte") : null;
            if (Objects.isNull(gte) || Objects.isNull(lte) || gte.compareTo(lte) <= 0) {
                source.put("salaryRange", new RangeDTO(gte, lte));
            }
        }

        // SET "department": "string"
        if (jobSharingJson.containsKey("department")) {
            source.put("department", jobSharingJson.getStr("department"));
        }

        // SET "requiredSkills": "string"
        if (jobSharingJson.containsKey("requiredSkills")) {
            source.put("requiredSkills", jobSharingJson.getJSONArray("requiredSkills"));
        }

        // SET "preferredSkills": "string"
        if (jobSharingJson.containsKey("preferredSkills")) {
            source.put("preferredSkills", jobSharingJson.getJSONArray("preferredSkills"));
        }

        // SET "preferredCurrency": "string"
        if (jobSharingJson.containsKey("preferredCurrency")) {
            source.put("preferredCurrency", jobSharingJson.getStr("preferredCurrency"));
        }


        //TODO: agencyJob, double check, too much info, no need
//        mergeKey(jobSharingJson, source);

    }

    private Instant getJobLastModifiedDate(JobV3 job) {

        Long id = job.getId();
        if (id == null) {
            return job.getLastModifiedDate();
        }
        return jobRepository.getJobEsLastModifiedTime(id);

    }

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    @Resource
    private TalentRelateJobFolderTalentRepository talentRelateJobFolderTalentRepository;

    private void setRelateJobFolder(JobV3 job, JSONObject source) {
        List<TalentAssociationJobFolder> jobFolder = talentRelateJobFolderRepository.getTalentRelateJobFoldersByJobIdIs(job.getId());
        if (!jobFolder.isEmpty()) {
            Map<String, List<TalentAssociationJobFolder>> folderIdGroup = jobFolder.stream().collect(Collectors.groupingBy(TalentAssociationJobFolder::getFolderId));
            List<FoldersOfPreSubmitTalents> array = new ArrayList<>();
            for (Map.Entry<String, List<TalentAssociationJobFolder>> entry : folderIdGroup.entrySet()) {
                String folderId = entry.getKey();
                List<TalentAssociationJobFolder> folders = entry.getValue();
                FoldersOfPreSubmitTalents object = new FoldersOfPreSubmitTalents();
                object.setFolderId(folderId);
                Instant createTime = getMinCreateTime(folders);
                object.setCreatedDate(DateUtil.fromInstantToUtcDateTime(createTime));
                object.setNumberOfTalents(talentRelateJobFolderTalentRepository.countTalentByFolderId(folderId));
                List<TalentAssociationJobFolder> ownerList = folders.stream().filter(f -> RelateJobFolderUserRole.OWNER.equals(f.getRole())).collect(Collectors.toList());
                if (!ownerList.isEmpty()) {
                    object.setResponsibility1(getRelateJobFolder(ownerList));
                }
                List<TalentAssociationJobFolder> shareList = folders.stream().filter(f -> RelateJobFolderUserRole.SHARER.equals(f.getRole())).collect(Collectors.toList());
                if (!shareList.isEmpty()) {
                    object.setResponsibility2(getRelateJobFolder(shareList));
                }
                List<TalentAssociationJobFolder> pendingList = folders.stream().filter(f -> RelateJobFolderStatus.PENDING.equals(f.getStatus())).collect(Collectors.toList());
                if (!pendingList.isEmpty()) {
                    object.setResponsibility3(getRelateJobFolder(pendingList));
                }
                array.add(object);
            }
            source.put("foldersOfPreSubmitTalents",array);
        }
    }

    private List<JobEsSyncDocument.Responsibility> getRelateJobFolder(List<TalentAssociationJobFolder> collect) {
        return collect.stream().map(f -> {
            JobEsSyncDocument.Responsibility responsibility = new JobEsSyncDocument.Responsibility();
            Long userId = f.getUserId();
            ResponseEntity<User> userRes = userService.findById(userId);
            if (userRes != null) {
                User body = userRes.getBody();
                if (body != null) {
                    responsibility.setId(String.valueOf(userId));
                    responsibility.setName(CommonUtils.formatFullNameWithBlankCheck(body.getFirstName(), body.getLastName()));
                }
            }
            return responsibility;
        }).collect(Collectors.toList());
    }

    private Instant getMinCreateTime(List<TalentAssociationJobFolder> folders) {
        TalentAssociationJobFolder minFolder = folders.stream()
                .min(Comparator.comparing(TalentAssociationJobFolder::getCreatedDate))
                .orElse(null);
        return minFolder != null ? minFolder.getCreatedDate() : null;
    }

    private void setJobNote(JSONObject source, JobV3 job) {
        List<JobNoteDTO> jobNotes = jobNoteService.findAllByJobId(job.getId());
        if (jobNotes.isEmpty()) {
            return;
        }

        List<JobEsSyncDocument.Note> esJobNotes = jobNotes.stream()
                .map(note -> {
                    JobEsSyncDocument.Note esNote = new JobEsSyncDocument.Note();
                    esNote.setId(note.getId().toString());
                    esNote.setText(HtmlUtil.cleanHtmlTag(note.getNote()));
                    esNote.setCreatedDate(DateUtil.fromInstantToUtcDateTime(note.getCreatedDate()));
                    esNote.setLastModifiedDate(DateUtil.fromInstantToUtcDateTime(note.getLastModifiedDate()));
                    if (ObjectUtil.isNotEmpty(note.getCreatedBy())) {
                        esNote.setResponsibility5(Arrays.asList(new JobEsSyncDocument.Responsibility(String.valueOf(note.getCreatedBy().getId()), null)));
                    }
                    if (ObjectUtil.isNotEmpty(note.getLastModifiedBy())) {
                        esNote.setResponsibility0(Arrays.asList(new JobEsSyncDocument.Responsibility(String.valueOf(note.getLastModifiedBy().getId()), null)));
                    }
                    return esNote;
                })
                .collect(Collectors.toList());
        source.put("notes", esJobNotes);
    }

//    private void setJobAdditionalInfo(JobV3 job, JSONObject source) {
//        JSONObject extendedJson = StringUtils.isNotEmpty(job.getJobExtendedInfo()) ? JSONUtil.parseObj(job.getJobExtendedInfo()) : new JSONObject();
//
//        if (CollUtil.isNotEmpty(extendedJson)) {
//            Object payType = extendedJson.getOrDefault("payType", null);
//            if (Objects.isNull(payType)) {
//                source.put("payType", RateUnitType.YEARLY.toString());
//            } else {
//                source.put("payType", String.valueOf(payType));
//            }
//            final JSONObject billRange = extendedJson.getJSONObject("billRange");
//            if (Objects.nonNull(billRange)) {
//                final BigDecimal gte = billRange.containsKey("gte") ? billRange.getBigDecimal("gte") : null;
//                final BigDecimal lte = billRange.containsKey("lte") ? billRange.getBigDecimal("lte") : null;
//                if (Objects.isNull(gte) || Objects.isNull(lte) || gte.compareTo(lte) <= 0) {
//                    source.put("billRange", new RangeDTO(gte, lte));
//                }
//            }
//
//            final JSONObject salaryRange = extendedJson.getJSONObject("salaryRange");
//            if (Objects.nonNull(salaryRange)) {
//                final BigDecimal gte = salaryRange.containsKey("gte") ? salaryRange.getBigDecimal("gte") : null;
//                final BigDecimal lte = salaryRange.containsKey("lte") ? salaryRange.getBigDecimal("lte") : null;
//                if (Objects.isNull(gte) || Objects.isNull(lte) || gte.compareTo(lte) <= 0) {
//                    source.put("salaryRange", new RangeDTO(gte, lte));
//                }
//            }
//
//            // TODO
//            //if (extendedJson.containsKey("sponsorWorkAuths")){
//            //    source.setSponsorWorkAuths(extendedJson.getJSONArray("sponsorWorkAuths").toJavaList(String.class));
//            //}
//
//            if (extendedJson.containsKey("department")) {
//                source.put("department", extendedJson.getStr("department"));
//            }
//
//            if (extendedJson.containsKey("requiredSkills")) {
//                source.put("requiredSkills", extendedJson.getJSONArray("requiredSkills"));
//            }
//
//            if (extendedJson.containsKey("preferredSkills")) {
//                source.put("preferredSkills", extendedJson.getJSONArray("preferredSkills"));
//            }
//
//            if (extendedJson.containsKey("preferredCurrency")) {
//                source.put("preferredCurrency", extendedJson.getStr("preferredCurrency"));
//            }
//
//            if (extendedJson.containsKey("requirements")) {
//                source.put("requirementText", HtmlUtil.cleanHtmlTag(extendedJson.getStr("requirements")));
//            }
//            if (extendedJson.containsKey("summary")) {
//                source.put("summaryText", HtmlUtil.cleanHtmlTag(extendedJson.getStr("summary")));
//            }
//            if (extendedJson.containsKey("responsibilities")) {
//                source.put("responsibilityText", HtmlUtil.cleanHtmlTag(extendedJson.getStr("responsibilities")));
//            }
//        }
////        //set jd keys
////        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getRequirements())) {
////            source.put("requirementText", HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getRequirements()));
////        }
////        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getSummary())) {
////            source.put("summaryText", HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getSummary()));
////        }
////        if (StringUtils.isNotBlank(job.getJobAdditionalInfo().getResponsibilities())) {
////            source.put("responsibilityText", HtmlUtil.cleanHtmlTag(job.getJobAdditionalInfo().getResponsibilities()));
////        }
//
//        mergeKey(extendedJson, source);
//    }

    private void setJobAdditionalInfo(JobV3 job, JSONObject source) {
        JSONObject extendedJson = StringUtils.isNotEmpty(job.getJobExtendedInfo())
                ? JSONUtil.parseObj(job.getJobExtendedInfo()) : new JSONObject();

        if (CollUtil.isNotEmpty(extendedJson)) {
            // ===== 特殊 key 先处理并 remove 掉 =====

            // payType
            Object payType = extendedJson.remove("payType");
            source.put("payType", Objects.isNull(payType) ? RateUnitType.YEARLY.toString() : String.valueOf(payType));

            // billRange
            handleRange(extendedJson, source, "billRange");
            extendedJson.remove("billRange");

            // salaryRange
            handleRange(extendedJson, source, "salaryRange");
            extendedJson.remove("salaryRange");

            // requirements / summary / responsibilities => HTML 去标签
            Map<String, String> htmlKeyMapping = Map.of(
                    "requirements", "requirementText",
                    "summary", "summaryText",
                    "responsibilities", "responsibilityText"
            );
            htmlKeyMapping.forEach((sourceKey, targetKey) -> {
                String value = Convert.toStr(extendedJson.remove(sourceKey), null);
                if (StringUtils.isNotEmpty(value)) {
                    source.put(targetKey, HtmlUtil.cleanHtmlTag(value));
                }
            });

            // ===== 剩余的 key 全部无脑同步 =====
            source.putAll(extendedJson);
        }

        mergeKey(extendedJson, source);
    }

//    private void setJobAdditionalInfo(JobV3 job, JSONObject source) {
//        JSONObject extendedJson = StringUtils.isNotEmpty(job.getJobExtendedInfo())
//                ? JSONUtil.parseObj(job.getJobExtendedInfo()) : new JSONObject();
//
//        if (CollUtil.isNotEmpty(extendedJson)) {
//            // ===== 特殊 key 先处理 =====
//
//            // payType
//            Object payType = extendedJson.getOrDefault("payType", null);
//            source.put("payType", Objects.isNull(payType) ? RateUnitType.YEARLY.toString() : String.valueOf(payType));
//
//            // billRange 和 salaryRange
//            handleRange(extendedJson, source, "billRange");
//            handleRange(extendedJson, source, "salaryRange");
//
//            // requirements / summary / responsibilities
//            Map<String, String> htmlKeyMapping = Map.of(
//                    "requirements", "requirementText",
//                    "summary", "summaryText",
//                    "responsibilities", "responsibilityText"
//            );
//            htmlKeyMapping.forEach((sourceKey, targetKey) -> {
//                if (extendedJson.containsKey(sourceKey)) {
//                    source.put(targetKey, HtmlUtil.cleanHtmlTag(extendedJson.getStr(sourceKey)));
//                }
//            });
//
//            // ===== 其他 key 统一处理 =====
//            Set<String> specialKeys = new HashSet<>(Arrays.asList(
//                    "payType", "billRange", "salaryRange",
//                    "requirements", "summary", "responsibilities"
//            ));
//
//            extendedJson.keySet().forEach(key -> {
//                if (!specialKeys.contains(key)) {
//                    source.put(key, extendedJson.get(key));
//                }
//            });
//        }
//
//        mergeKey(extendedJson, source);
//    }

    private void handleRange(JSONObject extendedJson, JSONObject source, String key) {
        final JSONObject obj = extendedJson.getJSONObject(key);
        if (Objects.nonNull(obj)) {
            final BigDecimal gte = obj.containsKey("gte") ? obj.getBigDecimal("gte") : null;
            final BigDecimal lte = obj.containsKey("lte") ? obj.getBigDecimal("lte") : null;
            if (Objects.isNull(gte) || Objects.isNull(lte) || gte.compareTo(lte) <= 0) {
                source.put(key, new RangeDTO(gte, lte));
            }
        }
    }

    private void mergeKey(JSONObject o1, JSONObject o2) {
        for (String key : o1.keySet()) {
            if (!o2.containsKey(key) && !USELESS_KEYS.contains(key)) {
                o2.put(key, o1.get(key));
            }
        }
    }

    private void addIpgAttributes(JSONObject esDocumentJson, JobV3 job) {
        if (JobType.PAY_ROLL != this.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId())) {
            SyncJobToIpgRelation oldSyncJobToIpgRelation = syncJobToIpgRelationRepository.findByApnJobId(job.getId());
            if (oldSyncJobToIpgRelation == null || oldSyncJobToIpgRelation.getIpgJobStatus() == JobStatus.CLOSED || oldSyncJobToIpgRelation.getIpgJobStatus() == JobStatus.NO_PUBLISHED) {
                esDocumentJson.put(ES_PUBLISHED_KEY, false);
            } else {
                esDocumentJson.put(ES_PUBLISHED_KEY, true);
            }
        }
    }


    private void addUserResponsibilityAttributes(JSONObject source, JobV3 job) {
        //find create user
        User createUser = null;
        if (ObjectUtil.isNotNull(job.getPermissionUserId())) {
            createUser = userService.findById(job.getPermissionUserId()).getBody();
        }

        List<String> affiliations = new ArrayList<>();
        //set affiliations for permission
        if (ObjectUtil.isNotNull(job.getPermissionUserId())) {
            affiliations.add(EsFillerConstants.ES_PUSER + job.getPermissionUserId());
        }
        if (ObjectUtil.isNotNull(job.getPermissionTeamId())) {
            affiliations.add(EsFillerConstants.ES_PTEAM + job.getPermissionTeamId());
        } else {
            affiliations.add("all");
        }
        source.put("affiliations", affiliations);

        if (ObjectUtil.isNotEmpty(createUser)) {
            // Set created by
            source.put("responsibility5", Arrays.asList(new JobEsSyncDocument.Responsibility(String.valueOf(createUser.getId()), CommonUtils.formatFullNameWithBlankCheck(createUser.getFirstName(), createUser.getLastName()))));
        }

        //SET application participants
        List<TalentRecruitmentProcessKpiUserVO> applicationUserList = applicationService.getKPIUsersByJobId(job.getId()).getBody();
        if (CollUtil.isNotEmpty(applicationUserList)) {
            List<Long> applicationUserIds = applicationUserList.stream().map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toList());
            List<UserBriefDTO> applicationUsers = userService.getAllBriefUsersByIds(applicationUserIds).getBody();
            source.put("responsibility12", applicationUsers
                    .stream()
                    .map(u -> new JobEsSyncDocument.Responsibility(String.valueOf(u.getId()), CommonUtils.formatFullNameWithBlankCheck(u.getFirstName(), u.getLastName())))
                    .collect(Collectors.toList()));
        }
        // SET Job participants
        final List<Long> participantIds = userJobRelationRepository.getParticipantIdsByJobId(job.getId());
        List<UserBriefDTO> jobParticipants = userService.getAllBriefUsersByIds(participantIds).getBody();
        if (CollectionUtils.isNotEmpty(participantIds)) {
            source.put("responsibility10", jobParticipants
                    .stream()
                    .map(u -> new JobEsSyncDocument.Responsibility(String.valueOf(u.getId()), CommonUtils.formatFullNameWithBlankCheck(u.getFirstName(), u.getLastName())))
                    .collect(Collectors.toList()));
        }

        //SET AM
        List<AssignedUserDTO> companyAMList = companyService.getApplicationUsers(job.getCompanyId()).getBody();
        if (CollUtil.isNotEmpty(companyAMList)) {
            // set AM
            source.put("responsibility1", companyAMList
                    .stream()
                    .filter(u -> JobPermission.AM.name().equals(u.getPermission()))
                    .map(am -> new JobEsSyncDocument.Responsibility(String.valueOf(am.getUserId()), CommonUtils.formatFullNameWithBlankCheck(am.getFirstName(), am.getLastName())))
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public HttpResponse searchFromCommonService(SearchGroup searchGroup, Pageable pageable) throws IOException {
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("JOB_SEARCH_CONDITION= " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        if (!hasSharedAgencySearchPermission(SecurityUtils.getUserId(), condition)) {
            return null;
        }
        String url = commonServiceUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = commonServiceUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    url += "&sort=" + convertESSortKeyToJobEsKey(sort.getProperty()) + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        Instant start = Instant.now();
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        log.info("[apn module={}, index={}, timeZone={}] EsFillerJobService.searchFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search talent from common service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if (ModuleType.JOB.getName().equals(searchGroup.getModule())) {
                    //search job by tenantId
                    countData = jobRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if (countData == 0) {
                    return new HttpResponse();
                } else {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsFillerTalentService @{}] search talent from common service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    private boolean hasSharedAgencySearchPermission(Long userId, String condition) {
        Pattern pattern = Pattern.compile("\"key\"\\s*:\\s*\"sharedAgencies\"");
        Matcher matcher = pattern.matcher(condition);
        if (matcher.find()) {
            return cachePermission.hasUserPrivilegePermission(userId, applicationProperties.getJobSearchAgencyPermissionUri());
        }
        return true;
    }

    @Override
    public HttpResponse searchRelateJobFolderFromCommonService(SearchGroup searchGroup, Pageable pageable) throws IOException {
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("JOB_SEARCH_CONDITION= " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        String url = commonServiceUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = commonServiceUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    url += "&sort=" + convertESSortKeyToRelateJobFolderEsKey(sort.getProperty()) + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        Instant start = Instant.now();
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        log.info("[apn module={}, index={}, timeZone={}] EsFillerJobService.searchFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search talent from common service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if (ModuleType.JOB.getName().equals(searchGroup.getModule())) {
                    //search job by tenantId
                    countData = jobRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if (countData == 0) {
                    return new HttpResponse();
                } else {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsFillerTalentService @{}] search talent from common service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    private String convertESSortKeyToRelateJobFolderEsKey(String sortKey) {
        if (StringUtils.isBlank(sortKey)) {
            return sortKey;
        }
        if ("createdDate".equals(sortKey)) {
            return "foldersOfPreSubmitTalents.createdDate";
        } else if (ResponsibilityConstants.OWNER.equals(sortKey)) {
            return "foldersOfPreSubmitTalents.responsibility1";
        } else if (ResponsibilityConstants.SHARED_BY.equals(sortKey)) {
            return "foldersOfPreSubmitTalents.responsibility2";
        }
        return sortKey;
    }

    @Override
    public String getJobDocument(Long jobId) {
        JobV3 jobV3 = jobRepository.findById(jobId).get();
        JobEsSyncDocument jobEsSyncDocument = buildJobProfile(jobV3, 0, true);
        return generateJsonString(jobEsSyncDocument);
    }

    @Override
    public void extractBulkJobToMq(Collection<Long> jobIds, int priority) {
        log.info("[EsFillerJobService: syncJobToMQ @{}] bulk jobProfiles start ids: {}", SecurityUtils.getUserId(), jobIds);
        List<JobEsSyncDocument> jobProfiles = jobRepository.findAllById(jobIds).stream().map(job -> buildJobProfile(job, 0, false)).collect(Collectors.toList());
        log.info("[EsFillerJobService: syncJobToMQ @{}] jobProfiles length: {}, ids: {}", SecurityUtils.getUserId(), jobProfiles.size(), jobIds);
        for (JobEsSyncDocument jobProfile : jobProfiles) {
            if (Objects.isNull(jobProfile)) {
                continue;
            }
            Long id = jobProfile.get_id();
            try {
                rabbitMqService.saveJobProfile(generateJsonString(jobProfile), priority);
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.JOB);
                log.info("[EsFillerJobService: syncJobToMQ @{}] bulk ave job to MQ success, id: {}, last modified date: {}", SecurityUtils.getUserId(), id, jobProfile.get_source().getStr("lastModifiedDate"));
            } catch (Exception e) {
                log.error("[EsFillerJobService: syncJobToMQ @{}] bulk save job to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send Job to ES Error" +
                        "\n\tJob ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void extractJobToMq(Collection<Long> jobIds, int priority) {
        log.info("[EsFillerJobService: syncJobToMQ @{}] jobProfiles start ids: {}", SecurityUtils.getUserId(), jobIds);
        List<JobEsSyncDocument> jobProfiles = jobRepository.findAllById(jobIds).stream().map(job -> buildJobProfile(job, 0, false)).collect(Collectors.toList());
        log.info("[EsFillerJobService: syncJobToMQ @{}] jobProfiles length: {}, ids: {}", SecurityUtils.getUserId(), jobProfiles.size(), jobIds);
        for (JobEsSyncDocument jobProfile : jobProfiles) {
            if (Objects.isNull(jobProfile)) {
                continue;
            }
            Long id = jobProfile.get_id();
            try {
                rabbitMqService.saveJobProfile(generateJsonString(jobProfile), priority);
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.JOB);
                invokeRaterByJob(jobProfile);
                log.info("[EsFillerJobService: syncJobToMQ @{}] save job to MQ success, id: {}, last modified date: {}", SecurityUtils.getUserId(), id, jobProfile.get_source().getStr("lastModifiedDate"));
            } catch (Exception e) {
                log.error("[EsFillerJobService: syncJobToMQ @{}] save job to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send Job to ES Error" +
                                 "\n\tJob ID: " + id +
                                 "\n\tError: " +
                                 "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    private void invokeRaterByJob(JobEsSyncDocument jobProfile){
        try {
            Optional<JobV3> jobOpt = jobRepository.findById(jobProfile.get_id());
            if(!jobOpt.isPresent()){
                return;
            }
            JobV3 job = jobOpt.get();
            if(job.getStatus().equals(JobStatus.OPEN)) {
                raterService.invokeRecommendCommonTalentsForJob(jobProfile.get_id(), Long.valueOf(jobProfile.get_tenant_id()));
                log.info("[EsFillerJobService: syncJobToMQ @{}] invoke rater, id: {}, : {}", SecurityUtils.getUserId(), jobProfile.get_id());
            }
        } catch (Exception e) {
            log.info("[EsFillerJobService: syncJobToMQ @{}] invoke rater, id: {}, job id: {}", SecurityUtils.getUserId(), jobProfile.get_id());
        }
    }

    private static String generateJsonString(JobEsSyncDocument jobProfile) {
        //TODO parser解析后有[null]  目前版本hutool处理不了 需要升级，升级修改过大， 先临时在同步时重新覆盖这2个值
        com.alibaba.fastjson.JSONArray requiredSkillsArray = JSON.parseArray(jobProfile.getRequiredSkills());
        jobProfile.setRequiredSkills(null);
        com.alibaba.fastjson.JSONArray preferredSkillsArray = JSON.parseArray(jobProfile.getPreferredSkills());
        jobProfile.setPreferredSkills(null);
        JSONObject source = jobProfile.get_source();
        com.alibaba.fastjson.JSONObject sourceFastJson = JSON.parseObject(JSONUtil.toJsonStr(source));
        if(requiredSkillsArray == null) {
            sourceFastJson.remove("requiredSkills");
        } else {
            sourceFastJson.put("requiredSkills", requiredSkillsArray);
        }
        if(preferredSkillsArray == null) {
            sourceFastJson.remove("preferredSkills");
        } else {
            sourceFastJson.put("preferredSkills", preferredSkillsArray);
        }
        com.alibaba.fastjson.JSONObject jobProfileFastJson = JSON.parseObject(JSONUtil.toJsonStr(jobProfile));
        jobProfileFastJson.put("_source", sourceFastJson);

        return jobProfileFastJson.toJSONString();
    }

    @Override
    public void extractJobToHrMq(Collection<Long> jobIds, int priority) {
        log.info("[EsFillerJobService: syncJobToHrMQ @{}] jobProfiles start ids: {}", SecurityUtils.getUserId(), jobIds);
        List<JSONObject> jobProfiles = jobRepository.findJobTitlesByIds(jobIds.stream().toList()).stream().map(this::buildJobHrProfile).toList();
        log.info("[EsFillerJobService: syncJobToHrMQ @{}] jobProfiles length: {}, ids: {}", SecurityUtils.getUserId(), jobProfiles.size(), jobIds);
        for (JSONObject jsonObject : jobProfiles) {
            if (Objects.isNull(jsonObject)) {
                continue;
            }
            Long id = jsonObject.getLong("id");
            try {
                jobdivaRabbitTemplate.convertAndSend(jobdivaRabbitProperties.getApnToJobdivaExchange(), jobdivaRabbitProperties.getApnToJobdivaRoutingKey(), JSONUtil.toJsonStr(jsonObject), message -> {
                    message.getMessageProperties().setPriority(priority);
                    return message;
                });
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.HR_JOB);
                log.info("[EsFillerJobService: syncJobToHrMQ @{}] save job to hr MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerJobService: syncJobToHrMQ @{}] save job to hr MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.HR_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send Job to hr Error" +
                        "\n\tJob ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void extractJobToAgencyMq(Collection<Long> jobIds, int priority) {
        log.info("[EsFillerJobService: syncJobToAgencyMQ @{}] jobProfiles start ids: {}", SecurityUtils.getUserId(), jobIds);

        //TODO:
        List<JobEsSyncDocument> jobProfiles = jobSharingToAgencyRepository.findAllWithStatus(jobIds).stream().map(j -> buildJobSharingToAgencyProfile(j, 0, false)).collect(Collectors.toList());

        log.info("[EsFillerJobService: syncJobToAgencyMQ @{}] jobProfiles length: {}, ids: {}", SecurityUtils.getUserId(), jobProfiles.size(), jobIds);
        for (JobEsSyncDocument jobProfile : jobProfiles) {
            if (Objects.isNull(jobProfile)) {
                continue;
            }
            Long id = jobProfile.get_id();
            try {
//                System.out.println("---------------------------------------------------");
//                System.out.println(JSONUtil.toJsonStr(jobProfile));
//                System.out.println("---------------------------------------------------");
                rabbitMqService.saveJobProfile(JSONUtil.toJsonStr(jobProfile), priority); //TODO: double check
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.AGENCY_JOB);
                log.info("[EsFillerJobService: syncJobToAgencyMQ @{}] save job to Agency MQ success, id: {}, last modified date: {}", SecurityUtils.getUserId(), id, jobProfile.get_source().getStr("lastModifiedDate"));
            } catch (Exception e) {
                log.error("[EsFillerJobService: syncJobToAgencyMQ @{}] save job to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.AGENCY_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send Agency Job to ES Error" +
                        "\n\tJob ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void deleteJobToMq(Long jobId, Long tenantId, int priority) {
        JobEsSyncDocument document = new JobEsSyncDocument();
        document.set_id(jobId);
        document.set_tenant_id(String.valueOf(tenantId));
        document.set_type("job");
        document.set_source(new JSONObject());
        document.set_deleted(Boolean.TRUE);
        rabbitMqService.saveJobProfile(JSONUtil.toJsonStr(document), priority);
        canalService.deleteByTaskIdAndType(jobId, SyncIdTypeEnum.JOB);
        log.info("[EsFillerJobService: syncJobToMQ @{}] delete job to MQ success, id: {}", SecurityUtils.getUserId(), jobId);

    }

    private JSONObject buildJobHrProfile(JobTitleVM job) {
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        JSONObject request = new JSONObject(jsonConfig);
        request.put("id", job.getJobId());
        request.put("type", JobdivaDataSyncTypeEnum.JOB);
        JSONObject entity = new JSONObject();
        entity.put("jobId", job.getJobId());
        entity.put("jobTitle", job.getJobTitle());
        request.put("entity", entity);
        return request;
    }

    /**
     * @param job
     * @param deep retry count
     * @param flag true 代表是job snapshot
     * @return
     */
    private JobEsSyncDocument buildJobProfile(JobV3 job, int deep, boolean flag) {
        //JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        //cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);
        JobEsSyncDocument document = new JobEsSyncDocument();
        try {
//            if (deep > 0){
//                Thread.sleep(2000);
//            }

            JSONObject source = this.buildEsSource(job, flag);

            if (flag) {
                source.put("jobId", job.getId());
            } else {
                document.set_id(job.getId());
            }
            //request.put("_index", "jobs_" + job.getTenantId());
            document.set_tenant_id(String.valueOf(job.getTenantId()));
            document.set_type("job");
            document.set_return_routing_key(esfillerMQProperties.getApnNormalizedJobRoutingKey());
            document.set_source(source);
            setNullArraySkill(document, job);
        } catch (Exception e) {
            log.error("[EsFillerJobService: syncJobToMQ @{}] buildJobProfile error, id: {}, error: {}", SecurityUtils.getUserId(), job.getId(), ExceptionUtils.getStackTrace(e));
            if (deep < esfillerMQProperties.getRetryThreshold()) {
                document = this.buildJobProfile(job, deep + 1, flag);
            } else {
                redisService.saveFailedJobIds(Arrays.asList(job.getId()));
                document = null;
                String message = "Build Job Profile Error" +
                                 "\n\tJob ID: " + job.getId() +
                                 "\n\tJob Title: " + job.getTitle() +
                                 "\n\tError: " +
                                 "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
        String json = JSONUtil.toJsonStr(document);
        return document;
    }

    private void setNullArraySkill(JobEsSyncDocument document, JobV3 job) {
        //TODO parser解析后有[null]  目前版本hutool处理不了 需要升级，升级修改过大， 先临时在同步时重新覆盖这2个值
        JobAdditionalInfo jobAdditionalInfo = job.getJobAdditionalInfo();
        if (ObjectUtil.isNotEmpty(jobAdditionalInfo)) {
            String extendedInfo = jobAdditionalInfo.getExtendedInfo();
            if (ObjectUtil.isNotEmpty(extendedInfo)) {
                com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extendedInfo);
                document.setPreferredSkills(jsonObject.getString("preferredSkills"));
                document.setRequiredSkills(jsonObject.getString("requiredSkills"));
            }
        }
    }

    /**
     * @param jobSharingToAgencyInfo job sharing to agency info with tenantId and jobStatus
     * @param retryCount
     * @param flag true 代表是job snapshot
     * @return
     */
    private JobEsSyncDocument buildJobSharingToAgencyProfile(JobSharingToAgencyWithStatusDTO jobSharingToAgencyInfo, int retryCount, boolean flag) {
        //JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        //cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);

        Long jobId = jobSharingToAgencyInfo.getJobId();
        Long tenantId = jobSharingToAgencyInfo.getTenantId();

        JobEsSyncDocument document = new JobEsSyncDocument();
        try {
//            if (deep > 0){
//                Thread.sleep(2000);
//            }

            JSONObject source = this.buildEsSourceForJobSharingToAgency(jobId, jobSharingToAgencyInfo.getStatus(), jobSharingToAgencyInfo.getRecruitmentProcessId(), jobSharingToAgencyInfo.getCompanyNameVisible(), jobSharingToAgencyInfo.getSharingToAgencyInfo(), jobSharingToAgencyInfo.getCompanyId(), jobSharingToAgencyInfo.getCompanyName(), jobSharingToAgencyInfo.getJobCreatedDate(), jobSharingToAgencyInfo.getPermissionUserId(), jobSharingToAgencyInfo.getPermissionTeamId(), flag);

            if (flag) {
                source.put("jobId", jobId);
            } else {
                document.set_id(jobId);
            }

            //TODO: here
            //request.put("_index", "jobs_" + job.getTenantId());
            document.set_tenant_id(String.valueOf(tenantId));
            document.set_type("agency_job");
//            document.set_return_routing_key(esfillerMQProperties.getApnNormalizedJobRoutingKey());
            document.set_source(source);

        } catch (Exception e) {
            log.error("[EsFillerJobService: syncJobToAgencyMQ @{}] buildJobProfile error, id: {}, error: {}", SecurityUtils.getUserId(), jobId, ExceptionUtils.getStackTrace(e));
            if (retryCount < esfillerMQProperties.getRetryThreshold()) {
                document = this.buildJobSharingToAgencyProfile(jobSharingToAgencyInfo, retryCount + 1, flag);
            } else {
                redisService.saveFailedAgencyJobIds(Arrays.asList(jobId));
                document = null;
                String message = "Build Agency Job Profile Error" +
                        "\n\tJob ID: " + jobId +
//                        "\n\tJob Title: " + job.getTitle() +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
        String json = JSONUtil.toJsonStr(document);
        return document;
    }

    @Override
    public void saveNormalizedJobInfos(String normalizedJob) {
        cn.hutool.json.JSONObject responseJson = JsonUtil.fromJson(JSONUtil.toJsonStr(normalizedJob), cn.hutool.json.JSONObject.class);
        Long jobId = responseJson.getLong("_id");
        JobV3 job = jobRepository.findById(jobId).orElse(new JobV3());
        if (ObjectUtil.isEmpty(job.getId())) {
            return;
        }
        cn.hutool.json.JSONObject jobJson = responseJson.getJSONObject("_source");
        if (ObjectUtil.isNotEmpty(jobJson)) {
            JSONArray locations = jobJson.getJSONArray(ElasticSearchConstants.ESFILLER_KEY_LOCATIONS);
            if (ObjectUtil.isNotEmpty(locations)) {
                List<LocationESDTO> locationDTOList = locations.toList(LocationESDTO.class);
                if (CollUtil.isNotEmpty(locationDTOList)) {
                    List<JobLocation> jobLocationList = new ArrayList<>();
                    locationDTOList.forEach(s -> {
                        if (ObjectUtil.isNotEmpty(s.getOfficialCity()) || ObjectUtil.isNotEmpty(s.getOfficialCountry())
                            || ObjectUtil.isNotEmpty(s.getOfficialProvince()) || ObjectUtil.isNotEmpty(s.getOfficialCounty())) {
                            if (ObjectUtil.isNotEmpty(s.getId())) {
                                jobLocationRepository.updateOfficialInfoById(s.getId(), s.getOfficialCity(), s.getOfficialCountry(), s.getOfficialProvince(), s.getOfficialCounty());
                            } else {
                                JobLocation location = new JobLocation();
                                location.setJobId(job.getId());
                                ServiceUtils.myCopyProperties(s, location);
                                jobLocationList.add(location);
                            }
                        }
                    });

                    if (CollUtil.isNotEmpty(jobLocationList)) {
                        jobLocationRepository.deleteAllByJobIdAndOriginalLoc(job.getId());
                        jobLocationRepository.saveAll(jobLocationList);
                    }
                }
            }
        }
        //jobFunctions
        Set<JobJobFunctionRelation> jobFunctionEnumSet = job.getJobFunctions();
        if (jobFunctionEnumSet == null || jobFunctionEnumSet.isEmpty()) {
            //cn.hutool.json.JSONObject jobJson = JSONUtil.parseObj(response);
            cn.hutool.json.JSONArray jobFunctionArray = jobJson.getJSONArray(ElasticSearchConstants.DROP_DOWN_JOBFUNCTION);
            List<Long> jobFunctions = getJobFunctinos(jobFunctionArray);
            if (jobFunctions != null && !jobFunctions.isEmpty()) {
                //job.setJobFunctions(new HashSet<>(Convert.toList(JobJobFunctionRelation.class, EnumRelationDTO.transfer(jobFunctions))));
                jobFunctionRelationRepository.saveAll(new HashSet<>(Convert.toList(JobJobFunctionRelation.class, EnumRelationDTO.transfer(jobFunctions))));
            }
        }
        //job.setLastSyncTime(Instant.now());
        //jobRepository.save(job);
        log.info("APN NormalizedJobInfos: jobId: {}, lastSyncTime: {}", jobId, Instant.now());
        jobRepository.updateJobLastSyncTime(jobId, Instant.now());
    }

    private List<Long> getJobFunctinos(JSONArray jobFunctionArray) {
        if (jobFunctionArray == null) {
            return null;
        }
        List<Long> ret = new ArrayList<>();
        for (int i = 0; i < jobFunctionArray.size(); i++) {
            ret.add(jobFunctionArray.getJSONObject(i).getLong("id"));
        }

        return ret;
    }

    private String convertESSortKeyToJobEsKey(String sortKey) {
        if (StringUtils.isBlank(sortKey)) {
            return sortKey;
        }
        Map<String, EnumUserResponsibility> enumUserResponsibilityMap = enumUserResponsibilityService.findActiveUserResponsibilityMap();
        if (enumUserResponsibilityMap.containsKey(sortKey)) {
            return enumUserResponsibilityMap.get(sortKey).getJobEsKey();
        }
        return sortKey;
    }

    @Override
    public Map<String, Long> getJobCategoryCount(JobCategoryCountRequestDTO requestDto) throws IOException {
        String url = categoryCountCommonServiceUrl();
        String requestBodyString = JSONUtil.toJsonStr(JSONUtil.parse(requestDto));
        HttpResponse response = httpService.post(url, requestBodyString);
        if (response == null || response.getBody() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.ELASTIC_GETJOBCATEGORYCOUNT_RESPONSENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.ELASTIC_GETJOBCATEGORYCOUNT_RESPONSESTATUSNOTOK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(response.getCode()), jobApiPromptProperties.getJobService()));
        }
        Type type = new TypeToken<Map<String, Long>>() {
        }.getType();
        return new Gson().fromJson(response.getBody(), type);
    }

    @Override
    public void updateJobFolder(List<Long> jobIds, List<String> toFolderIds, List<String> fromFolderIds, Long tenantId) {
        if (CollUtil.isEmpty(jobIds) || (toFolderIds == null && fromFolderIds == null)) {
            return;
        }

        JSONObject es = new JSONObject();
        es.put("ids", jobIds.stream().distinct().collect(Collectors.toList()));
        if (toFolderIds != null && CollUtil.isNotEmpty(toFolderIds)) {
            es.put("to", toFolderIds.stream().distinct().collect(Collectors.toList()));
        }
        if (fromFolderIds != null && CollUtil.isNotEmpty(fromFolderIds)) {
            es.put("from", fromFolderIds.stream().distinct().collect(Collectors.toList()));
        }
        if (ObjectUtil.isEmpty(es)) {
            return;
        }
        try {
            HttpResponse response = httpService.post(updateJobFolderUrl(tenantId), JSONUtil.toJsonStr(es));
            if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[APN: EsFillerJobService] updateJobFolder to EsFiller error,request body:{} , response code: {}, response message: {}", es, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                String message = "Update job folder to MQ Error" +
                                 "\n\tJob IDs: " + jobIds +
                                 "\n\tError: " +
                                 "\n\t" + response;
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        } catch (Exception e) {
            //redisService.saveFailedJobIds(jobIds);
            log.error("[APN: EsFillerJobService] updateJobFolder Exception, job = {}, message = {}", JSONUtil.toJsonStr(es), ExceptionUtils.getMessage(e));
            String message = "Update job folder to MQ Error" +
                             "\n\tJob IDs: " + jobIds +
                             "\n\tError: " +
                             "\n\t" + ExceptionUtils.getStackTrace(e);
            //NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
    }

    @Override
    public JobType getJobTypeByRecruitmentProcessId(Long recruitmentProcessId) {
        JobType jobType = Optional.of(applicationService.getRecruitmentProcessBriefById(recruitmentProcessId).getBody())
                .orElseThrow(
                        () -> new CustomParameterizedException("Did not find job type for recruitment process " + recruitmentProcessId)
                )
                .getJobType();
        return jobType;
    }
}
