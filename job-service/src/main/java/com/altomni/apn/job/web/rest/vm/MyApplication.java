package com.altomni.apn.job.web.rest.vm;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

/**
 * Created by <PERSON> on 9/19/2017.
 */
@Data
@Entity
public class MyApplication implements Serializable, AttachConfidentialTalent {

    @Id
    private Long id;

    private Long jobId;

    private String jobTitle;

    private Long companyId;

    private String company;

    private Long talentId;

    private String fullName;

    private String email;

    private String phone;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialTalentViewAble = true;
        this.confidentialInfo = confidentialInfo;
    }

    @Override
    public void encrypt(ConfidentialInfoDto confidentialInfo) {
       this.confidentialInfo = confidentialInfo;
       this.confidentialTalentViewAble = false;
       this.jobId = null;
       this.jobTitle = "";
       this.company = "";
       this.companyId = null;
       this.fullName = "";
       this.email = "";
       this.phone = "";
       this.lastModifiedBy = "";
       this.lastModifiedDate = null;
    }
}
