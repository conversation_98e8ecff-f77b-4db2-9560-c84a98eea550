package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.company.*;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "invoicing_application_info")
public class InvoicingApplicationInfo  extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 1、全职招聘开票申请  2、预付金发票 3、废票 */
    @Column(name = "invoicing_application_type")
    private Integer invoicingApplicationType ;

    /** 1、客户方付税款  2、我方付税款 */
    @Column(name = "tax_payer_type")
    private TaxPayerType taxPayerType;

    /** 废票 */
    @Column(name = "void_invoicing")
    private Integer voidInvoicing ;

    /** 1、系统添加 2、人工添加 */
    @Column(name = "prepayment_type")
    private Integer prepaymentType ;
    
    /** 原id */
    @Column(name = "invalid_invoicing_id")
    private Long invalidInvoicingId ;
    
    /** 废票开票原因 */
    @Column(name = "invoicing_reason")
    private String invoicingReason ;
    
    /** 编码 */
    @Column(name = "code_number")
    private String codeNumber ;
    
    /**  */
    @Column(name = "company_id")
    private Long companyId ;
    
    /**  */
    @Column(name = "tenant_id")
    private Long tenantId ;
    
    /** 客户开票信息 */
    @Column(name = "client_invoicing_id")
    private Long clientInvoicingId ;
    
    /** 1上海, 2深圳, 3尹泰 */
    @Column(name = "invoicing_body")
    @Convert(converter = InvoicingBodyConverter.class)
    private InvoicingBody invoicingBody ;
    
    /** 1增值税电子专业发票, 2增值税电子普通发票, 3抵扣发票 */
    @Column(name = "invoicing_type")
    @Convert(converter = InvoicingTypeConverter.class)
    private InvoicingType invoicingType ;
    
    /** 1FTE, 2RPO, 3STF-外包, 4STF-人事代理 */
    @Column(name = "invoicing_business_type")
    @Convert(converter = InvoicingBusinessTypeConverter.class)
    private InvoicingBusinessType invoicingBusinessType ;
    
    /** 开票内容名称 */
    @Column(name = "invoicing_server_tax_id")
    private Long invoicingServerTaxId ;
    
    /** 开票税率 */
    @Column(name = "invoicing_tax")
    private Double invoicingTax ;
    
    /** 发票格式 1pdf, 2ofd, 3xml */
    @Column(name = "invoice_format")
    private String invoiceFormat ;
    
    /** 确认函 */
    @Column(name = "confirmation_letter_url")
    private String confirmationLetterUrl ;

    /** 发票 */
    @Column(name = "invoice_url")
    private String invoiceUrl ;
    
    /** 开票总金额 */
    @Column(name = "invoicing_amount")
    private BigDecimal invoicingAmount ;
    
    /** 待回款金额 */
    @Column(name = "amount_due")
    private BigDecimal amountDue ;
    
    /** 发票税额 */
    @Column(name = "invoice_tax")
    private BigDecimal invoiceTax ;
    
    /** 不包含税额 */
    @Column(name = "taxes_not_included")
    private BigDecimal taxesNotIncluded ;
    
    /** 应开票总金额 */
    @Column(name = "gp_amount")
    private BigDecimal gpAmount ;
    
    /** 未开票金额 */
    @Column(name = "uninvoiced_amount")
    private BigDecimal uninvoicedAmount ;
    
    /** 待审批、审批驳回、待开票、已开票、未回款、部分回款、逾期、全部回款、已作废 */
    @Column(name = "invoicing_status")
    @Convert(converter = InvoicingStatusConverter.class)
    private InvoicingStatus invoicingStatus ;
    
    /** 数电票号码 */
    @Column(name = "electronic_invoice_number")
    private String electronicInvoiceNumber ;

    /** 开票日期 */
    @Column(name = "invoicing_date")
    private Instant invoicingDate ;

    /** 账单天数 */
    @Column(name = "due_within_days")
    private Integer dueWithinDays ;

    /** 到期日期 */
    @Column(name = "payment_due_date")
    private Instant paymentDueDate ;

    /** 开票备注 */
    @Column(name = "note")
    private String note ;

    @Column(name = "invoice_note")
    private String invoiceNode;

    @Column(name = "status")
    private Integer status;

    /** 审批通过备注 */
    @Column(name = "approval_note")
    private String approvalNote;
}
