package com.altomni.apn.finance.web.rest.invoicing;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.company.InvoicingBusinessType;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.dto.invoicing.*;
import com.altomni.apn.finance.service.invoicing.InvoicingApplicationInfoService;
import com.altomni.apn.finance.service.vo.invoicing.InvoicingApplicationInfoVO;
import com.altomni.apn.finance.service.vo.invoicing.InvoicingApplicationInfoViewVO;
import com.altomni.apn.finance.service.vo.invoicing.InvoicingApplicationPrepaymentVO;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
@Transactional
@Slf4j
public class InvoicingApplicationInfoResource {

    @Resource
    InvoicingApplicationInfoService invoicingApplicationInfoService;


    @PostMapping("/invoicing/application/search")
    public ResponseEntity<List<InvoicingApplicationInfoVO>> search(@RequestBody InvoicingApplicationInfoSearchDTO dto, @PageableDefault Pageable pageable) {
        log.info("[APN: Invoicing @{}] REST request to get all Invoicing info", SecurityUtils.getUserId());
        Page<InvoicingApplicationInfoVO> result = invoicingApplicationInfoService.searchAll(dto,pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(result, "/api/v3/invoicing/application/search");
        return ResponseEntity.ok().headers(headers).body(result.getContent());
    }

    @PostMapping("/invoicing/application/download")
    public ResponseEntity download(HttpServletResponse response,@RequestBody InvoicingApplicationInfoSearchDTO dto) {
        log.info("[APN: Invoicing @{}] REST request to download all Invoicing info", SecurityUtils.getUserId());
        invoicingApplicationInfoService.download(response,dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * 2.3.4 查询预付金金额
     * @param companyId
     * @param invoicingBusinessType
     * @return
     */
    @GetMapping("/invoicing/application/prepayment/{companyId}/{invoicingBusinessType}")
    public ResponseEntity<InvoicingApplicationPrepaymentVO> search(@PathVariable Long companyId,@PathVariable InvoicingBusinessType invoicingBusinessType) {
        log.info("[APN: Invoicing @{}] REST request to get prepayment amount", SecurityUtils.getUserId());
        InvoicingApplicationPrepaymentVO result = invoicingApplicationInfoService.findPrepaymentByCompanyId(companyId,invoicingBusinessType);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(result));
    }


    @PostMapping("/invoicing/application")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<InvoicingApplicationInfoViewVO> save(@RequestBody InvoicingApplicationInfoDTO dto) {
        log.info("[invoicing: User @{}] REST create invoice method:", SecurityUtils.getUserId());
        InvoicingApplicationInfoViewVO vo = invoicingApplicationInfoService.save(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(vo);
    }

    @PutMapping("/invoicing/application")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<InvoicingApplicationInfoViewVO> modify(@RequestBody InvoicingApplicationInfoDTO dto) {
        log.info("[invoicing: User @{}] REST update invoicing method:", SecurityUtils.getUserId());
        InvoicingApplicationInfoViewVO vo = invoicingApplicationInfoService.modify(dto);
        return ResponseEntity.status(HttpStatus.OK).body(vo);
    }

    /**
     * 2.3.8 查看全职招聘开票申请
     * @param id
     * @return
     */
    @GetMapping("/invoicing/application/{id}")
    @Timed
    public ResponseEntity<InvoicingApplicationInfoViewVO> getById(@PathVariable Long id) {
        log.info("[invoicing: User @{}] REST get invoicing method:", SecurityUtils.getUserId());
        InvoicingApplicationInfoViewVO vo = invoicingApplicationInfoService.view(id);
        return ResponseEntity.status(HttpStatus.OK).body(vo);
    }

    /**
     * 2.3.9 撤回开票申请
     * @param dto
     * @return
     */
    @PutMapping("/invoicing/void")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> voidInvoicing(@RequestBody InvoicingApplicationInfoDTO dto) {
        log.info("[invoicing: User @{}] REST void invoicing method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.voidInvoicing(dto.getId());
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.10 财务审批接口
     * @param dto
     * @return
     */
    @PostMapping("/invoicing/financial/approval")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialApproval(@RequestBody InvoicingFinancialApprovalDTO dto) {
        log.info("[invoicing: User @{}] REST financial approval method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialApproval(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.11 财务批量审批接口
     * @param dto
     * @return
     */
    @PostMapping("/invoicing/financial/approval/list")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialApprovalList(@RequestBody InvoicingFinancialApprovalDTO dto) {
        log.info("[invoicing: User @{}] REST financial approval list method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialApprovalList(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.12 记录开票信息
     * @param dto
     * @return
     */
    @PostMapping("/invoicing/financial/record")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialInvoicingRecord(@RequestBody InvoicingFinancialRecordDTO dto) {
        log.info("[invoicing: User @{}] REST financial approval list method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialInvoicingRecord(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.13 记录开票信息（废票）
     * @param dto
     * @return
     */
    @PostMapping("/invoicing/financial/void-record")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialInvoicingVoidRecord(@RequestBody InvoicingFinancialRecordDTO dto) {
        log.info("[invoicing: User @{}] REST financial approval list method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialInvoicingVoidRecord(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.14 添加财务记录回款接口
     * @param dto
     * @return
     */
    @PostMapping("/invoicing/financial/record-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialRecordPayment(@RequestBody InvoicingFinancialRecordPaymentDTO dto) {
        log.info("[invoicing: User @{}] REST financial record payment method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialRecordPayment(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.15 修改财务记录回款接口
     * @param dto
     * @return
     */
    @PutMapping("/invoicing/financial/record-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> modifyFinancialRecordPayment(@RequestBody InvoicingFinancialRecordPaymentDTO dto) {
        log.info("[invoicing: User @{}] REST modify financial record payment method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.modifyFinancialRecordPayment(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.16 财务批量记录回款接口
     * @param dto
     * @return
     */
    @PostMapping("/invoicing/financial/bulk-record-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialBulkRecordPayment(@RequestBody InvoicingFinancialRecordPaymentDTO dto) {
        log.info("[invoicing: User @{}] REST financial bulk record payment method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialBulkRecordPayment(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 2.3.17 财务取消记录回款接口
     * @param dto
     * @return
     */
    @PutMapping("/invoicing/financial/unreocrd-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> financialUnrecordPayment(@RequestBody InvoicingFinancialRecordPaymentDTO dto) {
        log.info("[invoicing: User @{}] REST financial unreocrd payment method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.financialUnrecordPayment(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 5.10 未结清关闭接口
     * @param dto
     * @return
     */
    @PutMapping("/invoicing/outstanding-close")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> outstandingClose(@RequestBody InvoicingOutstandingCloseDTO dto) {
        log.info("[invoicing: User @{}] REST outstanding close method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.outstandingClose(dto);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 扫描逾期数据并更新
     * @return
     */
    @PostMapping("/invoicing/xxl-job/update-invoicing-status")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> updateInvoicingStatus() {
        log.info("[invoicing: User @{}] REST update invoicing status method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.updateInvoicingStatus();
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 刷新历史候选人支付信息
     * @return
     */
    @PostMapping("/invoicing/init-talent-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> initTalentPayment() {
        log.info("[invoicing: User @{}] REST init talent payment method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.initTalentPayment();
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * 刷新候选人应收金额含税
     * @return
     */
    @PostMapping("/invoicing/init-talent-tax")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> initCandidateTax() {
        log.info("[invoicing: User @{}] REST init talent payment method:", SecurityUtils.getUserId());
        invoicingApplicationInfoService.initCandidateTax();
        return ResponseEntity.status(HttpStatus.OK).build();
    }
}
