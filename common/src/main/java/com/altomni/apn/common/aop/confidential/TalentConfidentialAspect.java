package com.altomni.apn.common.aop.confidential;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.service.talent.TalentFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerErrorException;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
public class TalentConfidentialAspect {

    private final TalentFeignClient talentFeignClient;

    public TalentConfidentialAspect(TalentFeignClient talentFeignClient) {
        this.talentFeignClient = talentFeignClient;
    }

    @Pointcut("@annotation(ProcessConfidentialTalent)")
    public void applicationPointcut() {
    }

    @Around("applicationPointcut()")
    public Object attachPointcut(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("process confidential talent");
        Object returnVal = joinPoint.proceed();
        boolean isResponseEntity = returnVal instanceof ResponseEntity<?>;
        ResponseEntity<?> responseEntity = isResponseEntity ? (ResponseEntity<?>) returnVal : null;
        Object result = isResponseEntity ? responseEntity.getBody() : returnVal;
        ProcessConfidentialTalent annotation = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(ProcessConfidentialTalent.class);
        if (annotation == null) {
            return returnVal;
        }
        Object object = processResponseBody(result, annotation);
        if (isResponseEntity) {
            return ResponseEntity.status(responseEntity.getStatusCode())
                    .headers(responseEntity.getHeaders())
                    .body(object);
        } else {
            return object;
        }
    }

    private Object processResponseBody(Object body, ProcessConfidentialTalent annotation) {
        if (body instanceof AttachConfidentialTalent entity) {
            return processConfidentialTalent(annotation, Collections.singletonList(entity)).stream().findFirst().orElse(null);
        }

        if (body instanceof Collection<?> collection && collection.stream().allMatch(item -> item instanceof AttachConfidentialTalent)) {
            return processConfidentialTalent(annotation, (Collection<? extends AttachConfidentialTalent>) collection);
        }

        if (body instanceof Page<?> page) {
            // 处理分页结果内容
            Collection<?> content = page.getContent();
            if (!content.isEmpty() && content.stream().allMatch(item -> item instanceof AttachConfidentialTalent)) {
                Collection<?> objects = processConfidentialTalent(annotation, (Collection<? extends AttachConfidentialTalent>) content);
                return new PageImpl<>(new ArrayList<>(objects), page.getPageable(), page.getTotalElements());
            }
        }
        return body;
    }

    private Collection<?> processConfidentialTalent(ProcessConfidentialTalent annotation, Collection<? extends AttachConfidentialTalent> entities) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }
        List<? extends AttachConfidentialTalent> validEntities = entities.stream()
                .filter(entity -> entity.getTalentId() != null)
                .toList();
        if (validEntities.isEmpty()) {
            return entities;
        }
        Set<Long> talentIds = validEntities.stream()
                .map(AttachConfidentialTalent::getTalentId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        if (talentIds.isEmpty()) {
            return entities;
        }
        Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentFeignClient.getTalentConfidentialInfo(talentIds).getBody();
        if (confidentialInfoMap == null || confidentialInfoMap.isEmpty()) {
            log.info("no confidential info found for talents: {}", confidentialInfoMap);
            return entities;
        }
        Set<Long> viewableTalentIds = talentFeignClient.filterConfidentialTalentViewAble(talentIds).getBody();
        if (viewableTalentIds == null) {
            log.info("no view able confidential talent found");
            throw new ServerErrorException("Internal Server Error", (Throwable) null);
        }
        List<? extends AttachConfidentialTalent> list = validEntities.stream()
                .peek(r -> {
                    // 候选人是保密候选人
                    if (confidentialInfoMap.containsKey(r.getTalentId())) {
                        r.setConfidentialInfo(confidentialInfoMap.get(r.getTalentId()));
                        r.setConfidentialTalentViewAble(viewableTalentIds.contains(r.getTalentId()));

                    }
                }).toList();
        if (annotation.operation() == ProcessConfidentialTalent.operation.FILTER) {
            log.info("filter confidential talent");
            return list.stream()
                    .filter(entity -> viewableTalentIds.contains(entity.getTalentId()))
                    .toList();
        } else {
            log.info("attach confidential info");
            return list.stream()
                    .peek(t -> {
                        if (t.getConfidentialTalentViewAble()!= null && !t.getConfidentialTalentViewAble()) {
                           t.encrypt();
                        }
                    })
                    .toList();
        }
    }

    private Object extractResultObj(Object returnVal) {
        if (returnVal instanceof ResponseEntity<?> responseEntity) {
            return responseEntity.getBody();
        } else {
            return returnVal;
        }
    }

}
