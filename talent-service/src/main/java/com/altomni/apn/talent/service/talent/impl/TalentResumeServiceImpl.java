package com.altomni.apn.talent.service.talent.impl;
/*
 * Created by <PERSON> on 10/30/2017.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.CommonDataStatus;
import com.altomni.apn.common.domain.enumeration.RecommendFeedbackReason;
import com.altomni.apn.common.domain.talent.Resume;
import com.altomni.apn.common.domain.talent.TalentResumeRelation;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.redis.ImagesInfoDTO;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.application.TalentApplicationProcessSubmitToJob;
import com.altomni.apn.talent.repository.application.TalentApplicationProcessSubmitToJobRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.repository.talent.TalentResumeRelationRepository;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.talent.service.elastic.EsCommonService;
import com.altomni.apn.talent.service.redis.RedisService;
import com.altomni.apn.talent.service.store.StoreService;
import com.altomni.apn.talent.service.talent.TalentResumeService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.service.talent.TalentServiceV3;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.zalando.problem.Status;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.Constants.*;

@Service
public class TalentResumeServiceImpl implements TalentResumeService {

    private final Logger log = LoggerFactory.getLogger(TalentResumeServiceImpl.class);

    @Resource
    private ResumeRepository resumeRepository;

    @Resource
    private TalentResumeRelationRepository talentResumeRelationRepository;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private StoreService storeService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    private TalentConfidentialService talentConfidentialService;


    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public Resume saveResume(Resume resume) {
        Resume resumeLocal = resumeRepository.findByUuid(resume.getUuid());
        if (resumeLocal != null) {
            //special treatment hasPortrait、hasDisplay
            Boolean hasPortrait = resumeLocal.getHasPortrait();
            if (ObjectUtil.isNotNull(resume.getHasPortrait())) {
                hasPortrait = resume.getHasPortrait() || resumeLocal.getHasPortrait();
            }
            ServiceUtils.myCopyProperties(resumeLocal, resume);
            resumeLocal.setHasPortrait(hasPortrait);
            resumeLocal.setNPages(resume.getNPages());
        }
        return resumeRepository.save(resume);
    }

    @Resource
    private RedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public TalentResumeDTO createTalentResume(TalentResumeDTO talentResumeDTO) {
        if (talentResumeDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATE_TALENTCONTACTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Long talentId = talentResumeDTO.getTalentId();
        TalentV3 talent = talentRepository.findById(talentId).orElseThrow(() -> new NotFoundException("Talent not found."));

        if (!SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if (StringUtils.isEmpty(talentResumeDTO.getUuid())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENTRESUME_CREATETALENTRESUME_UUIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Resume resume = resumeRepository.findByUuid(talentResumeDTO.getUuid());
        if(resume == null) {
            resume = new Resume();
            String uuid = talentResumeDTO.getUuid();
            ParserRedisResponse response = redisService.getParserResumeData(uuid);
            if (ObjectUtil.isNotEmpty(response.getStatus()) && ParseStatus.FINISHED.equals(response.getStatus())) {
                if (ObjectUtil.isNotEmpty(response.getData())) {
                    JSONObject jsonObject = JSONUtil.parseObj(response.getData());
                    resume.setParseResult(response.getData());
                    resume.setText(jsonObject.getStr("text"));
                    resume.setSkillsText(jsonObject.getStr("skillsText"));
                }
                //update resume imageInfo
                if (ObjectUtil.isNotEmpty(response.getImagesInfo())) {
                    if (ObjectUtil.isNotNull(response.getImagesInfo().getHas_portrait())) {
                        resume.setHasPortrait(BooleanUtil.toBoolean(response.getImagesInfo().getHas_portrait().toString()));
                    }
                    resume.setNPages(response.getImagesInfo().getN_pages());
                }
            }
            //create resume
            resume.setUuid(uuid);
            resumeRepository.save(resume);
        }
        Long otherTalentId = resumeBindByOther(resume.getId(), talentId);
        if(otherTalentId != null) {
            throw new WithDataException("Duplicate entry talent data.", cn.hutool.http.Status.HTTP_PRECON_FAILED, List.of(talentService.getResumeDuplicate(otherTalentId)));
        }
        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findByResumeIdAndTenantIdAndTalentId(resume.getId(), talent.getTenantId(), talentId);
        if (talentResumeRelation == null) {
            talentResumeRelation = new TalentResumeRelation();
            talentResumeRelation.setResumeId(resume.getId());
            talentResumeRelation.setTalentId(talentResumeDTO.getTalentId());
            talentResumeRelation.setFileName(talentResumeDTO.getFileName());
            talentResumeRelation.setSourceType(talentResumeDTO.getSourceType());
            talentResumeRelation.setTenantId(SecurityUtils.getTenantId());
            talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
        } else {
            talentResumeRelation.setStatus(CommonDataStatus.AVAILABLE);
        }
        talentResumeRelationRepository.save(talentResumeRelation);
//        talentRepository.updateTalentLastEditedTimeAndOwnedDate(talentId, talent.getTenantId());
        talentResumeDTO.setId(talentResumeRelation.getId());
        talentResumeDTO.setImagesInfo(getImagesInfo(resume.getUuid()));
        updateTalentLastEditedTimeAndOwnedData(talentId);
        return talentResumeDTO;
    }

    private Long resumeBindByOther(Long resumeId, Long excludeTalentId) {
        List<TalentResumeRelation> talentResumeRelationList = talentResumeRelationRepository.findAllByResumeIdIsAndTenantIdIsAndStatusIs(resumeId, SecurityUtils.getTenantId(), CommonDataStatus.AVAILABLE);
        for(TalentResumeRelation relation : talentResumeRelationList) {
            if(!excludeTalentId.equals(relation.getTalentId())) {
                return relation.getTalentId();
            }
        }

        return null;
    }

    private void updateTalentLastEditedTimeAndOwnedData(Long talentId) {
        TalentV3 talentV3 = talentRepository.findById(talentId).orElse(null);
        if(talentV3 != null) {
            talentV3.setLastEditedTime(Instant.now());
            talentV3.setOwnedByTenants(talentV3.getTenantId());
            talentRepository.saveAndFlush(talentV3);
        }
    }

    private ImagesInfoDTO getImagesInfo(String uuid) {
        ImagesInfoDTO imagesInfoDTO = new ImagesInfoDTO();
        try {
            //format s3Link and return contentType
            CloudFileObjectMetadata cloudFileObjectMetadata = storeService.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.RESUME_DISPLAYS.getKey()).getBody();
            if(cloudFileObjectMetadata == null) {
                //format s3Link and return contentType
                cloudFileObjectMetadata = storeService.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody();
            }
            String contentType = null;
            if (cloudFileObjectMetadata != null) {
                contentType = cloudFileObjectMetadata.getContentType();
            }
            //if RESUME_CONTENT_TYPE_ORIGINAL_RETURN or RESUME_CONTENT_TYPE_NO_NEED_TO_RESET
            if (RESUME_CONTENT_TYPE_ORIGINAL_RETURN.contains(contentType) || RESUME_CONTENT_TYPE_NO_NEED_TO_RESET.contains(contentType)) {
                setDisplayResumeInfos(uuid, imagesInfoDTO, contentType, false);
                //if RESUME_CONTENT_TYPE_NEED_TO_RESET
            } else if (RESUME_CONTENT_TYPE_NEED_TO_RESET.contains(contentType)) {
                setDisplayResumeInfos(uuid, imagesInfoDTO, null, true);
            } else {
                setDisplayResumeInfos(uuid, imagesInfoDTO, contentType, false);
            }
        } catch (Exception e) {
            log.info("info: get presigned url from amazon s3 for talent resume S3Link, uuid: {}", uuid);
        }
        return imagesInfoDTO;
    }

    @Autowired
    private TalentService talentService;

    @Resource
    private TalentApplicationProcessSubmitToJobRepository submitToJobRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public TalentResumeRelation deleteTalentResume(Long id) {
        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findById(id).orElseThrow(() -> new NotFoundException("The TalentResumeDTO not found."));

        if (!Objects.equals(SecurityUtils.getTenantId(), talentResumeRelation.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        List<TalentApplicationProcessSubmitToJob> bindSubmitToJob = submitToJobRepository.findAllByTalentResumeRelationIdIs(id);
        if(!bindSubmitToJob.isEmpty()) {
            throw new CustomParameterizedException("The resume has been submitted to the job.");
        }

        talentResumeRelation.setStatus(CommonDataStatus.INVALID);
        talentResumeRelationRepository.save(talentResumeRelation);
        updateTalentLastEditedTime(talentResumeRelation.getTalentId());
        return talentResumeRelation;
    }

    private void updateTalentLastEditedTime(Long talentId) {
        TalentV3 talentV3 = talentRepository.findById(talentId).orElse(null);
        if(talentV3 != null) {
            talentV3.setLastEditedTime(Instant.now());
            talentRepository.saveAndFlush(talentV3);
        }
    }

    @Override
    public TalentResumeDTO findOne(Long id) {

        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findById(id).orElseThrow(() -> new NotFoundException("The TalentResumeRelation not found."));
        Long talentId = talentResumeRelation.getTalentId();

        if (!talentService.hasTalentViewAuthority(talentId)) {
            throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No client contact access permission");
        }
        if (!talentConfidentialService.confidentialTalentViewAble(talentId)) {
            throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No talent contact access permission");
        }

        if(!CommonDataStatus.AVAILABLE.equals(talentResumeRelation.getStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENTRESUME_FINDONE_RESUMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Resume resume = resumeRepository.findById(id).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENTRESUME_FINDONE_RESUMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService())));
        ImagesInfoDTO imagesInfoDTO = getImagesLinks(resume);
        return generateTalentResumeDTO(talentResumeRelation, resume, imagesInfoDTO);
    }

    @Override
    public TalentResumeDTO findOneByTalentResumeRelationId(Long relationId) {
        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findById(relationId).orElseThrow(() -> new NotFoundException("The TalentResumeRelation not found."));
        Resume resume = resumeRepository.findById(talentResumeRelation.getResumeId()).orElseThrow(() -> new NotFoundException("The Resume not found."));
        ImagesInfoDTO imagesInfoDTO = getImagesLinks(resume);
        return generateTalentResumeDTO(talentResumeRelation, resume, imagesInfoDTO);
    }

    private ImagesInfoDTO getImagesLinks(Resume resume) {
        ImagesInfoDTO imagesInfoDTO = new ImagesInfoDTO();
        //1.format portrait s3Link
        if (ObjectUtil.isNotNull(resume.getHasPortrait()) && resume.getHasPortrait()) {
            try {
                if (BooleanUtil.isTrue(storeService.exists(resume.getUuid() + Constants.S3_KEY_PORTRAIT_SUFFIX, UploadTypeEnum.PORTRAIT.getKey()).getBody())) {
                    imagesInfoDTO.setPortraitLink(storeService.getUrlFromS3(resume.getUuid() + Constants.S3_KEY_PORTRAIT_SUFFIX, UploadTypeEnum.PORTRAIT.getKey()).getBody());
                }
            } catch (Exception e) {
                log.info("info: get presigned url from amazon s3 for talent resume portrait S3Link, uuid: {}", resume.getUuid());
            }
        }

        //2.format s3Link and return contentType
        CloudFileObjectMetadata cloudFileObjectMetadata = storeService.getFileDetailWithoutFileFromS3(resume.getUuid(), UploadTypeEnum.RESUME.getKey()).getBody();
        String contentType = null;
        if (cloudFileObjectMetadata != null) {
            contentType = cloudFileObjectMetadata.getContentType();
        }
        //if RESUME_CONTENT_TYPE_ORIGINAL_RETURN
        if (RESUME_CONTENT_TYPE_ORIGINAL_RETURN.contains(contentType)) {
            //if has display s3Link, use display s3Link
            CloudFileObjectMetadata displayResumeMetadata = storeService.getFileDetailWithoutFileFromS3(resume.getUuid() , UploadTypeEnum.RESUME_DISPLAYS.getKey()).getBody();
            if (displayResumeMetadata != null) {
                imagesInfoDTO.setDisplayLink(displayResumeMetadata.getS3Link());
                imagesInfoDTO.setContentType(displayResumeMetadata.getContentType());
            } else {
                setDisplayResumeInfos(resume.getUuid(), imagesInfoDTO, contentType, false);
            }
            //if RESUME_CONTENT_TYPE_NO_NEED_TO_RESET
        } else if (RESUME_CONTENT_TYPE_NO_NEED_TO_RESET.contains(contentType)) {
            setDisplayResumeInfos(resume.getUuid(), imagesInfoDTO, contentType, false);
            //if RESUME_CONTENT_TYPE_NEED_TO_RESET
        } else if (RESUME_CONTENT_TYPE_NEED_TO_RESET.contains(contentType)) {
            setDisplayResumeInfos(resume.getUuid(), imagesInfoDTO, null, true);
        }else{
            setDisplayResumeInfos(resume.getUuid(), imagesInfoDTO, contentType, false);
        }
        return imagesInfoDTO;
    }

    private void setDisplayResumeInfos(String uuid, ImagesInfoDTO imagesInfoDTO, String contentType, Boolean needToReset) {
        try {
            String url = storeService.getUrlFromS3(uuid, UploadTypeEnum.RESUME_DISPLAYS.getKey()).getBody();
            if(url != null) {
                imagesInfoDTO.setContentType("application/pdf");
                imagesInfoDTO.setDisplayLink(url);
            } else {
                if (needToReset) {
                    imagesInfoDTO.setContentType(CONTENT_TYPE_MSWORD);
                    imagesInfoDTO.setDisplayLink(storeService.getDisplayUrlFromS3WithContentType(uuid, UploadTypeEnum.RESUME.getKey(), imagesInfoDTO.getContentType()).getBody());
                } else {
                    imagesInfoDTO.setContentType(contentType);
                    imagesInfoDTO.setDisplayLink(storeService.getUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody());
                }
            }
        } catch (Exception e) {
            log.info("info: get presigned url from amazon s3 for talent resume S3Link, uuid: {}", uuid);
        }
    }

//    private ImagesInfoDTO getImagesLinksWithoutPortrait(Resume resume) {
//        ImagesInfoDTO imagesInfoDTO = new ImagesInfoDTO();
//        //if has not display s3Link, use original s3Link
//        if (ObjectUtil.isNotNull(resume.getHasDisplay()) && resume.getHasDisplay()) {
//            try {
//                String contentType = storeService.isExistsDisplayResume(resume.getUuid() + Constants.S3_KEY_RESUME_PDF_SUFFIX).getBody();
//                if (ObjectUtil.isNotNull(contentType) && (Constants.CONTENT_TYPE_PDF.equals(contentType) || StrUtil.toString(MediaType.PLAIN_TEXT_UTF_8).equals(contentType))) {
//                    imagesInfoDTO.setDisplayLink(storeService.getPresignedDisplayResumeUrlFromS3(resume.getUuid() + Constants.S3_KEY_RESUME_PDF_SUFFIX).getBody());
//                }
//            } catch (Exception e) {
//                log.info("info: get presigned url from amazon s3 for talent resume display S3Link, uuid: {}", resume.getUuid());
//            }
//        } else {
//            try {
//                //check source file contentType, if not "application/pdf" or "text/plain", return null
//                String contentType = storeService.isExistsResume(resume.getUuid()).getBody();
//                if (ObjectUtil.isNotNull(contentType) && (Constants.CONTENT_TYPE_PDF.equals(contentType) || StrUtil.toString(MediaType.PLAIN_TEXT_UTF_8).equals(contentType))) {
//                    imagesInfoDTO.setDisplayLink(storeService.getPresignedResumeUrlFromS3(resume.getUuid()).getBody());
//                }
//            } catch (Exception e) {
//                log.info("info: get presigned url from amazon s3 for talent resume S3Link, uuid: {}", resume.getUuid());
//            }
//        }
//        return imagesInfoDTO;
//    }

    @Resource
    private EsCommonService esCommonService;

    @Override
    public TalentResumeOutput findAllByTalentId(String talentId, Long jobId) {
        TalentResumeOutput output = new TalentResumeOutput();
        //兼容推荐时搜索commonpool id的联系方式
        if(!NumberUtil.isNumber(talentId)) {
            try {
                TalentDTOV3 talent = esCommonService.getTalent(talentId);
                output.setTalentData(JSONUtil.toJsonStr(talent));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            output.setResumeInfo(new ArrayList<>());

        } else {
            Long lTalentId = Long.valueOf(talentId);
            if (!talentService.hasTalentViewAuthority(lTalentId)) {
                throw new CustomParameterizedException(Status.FORBIDDEN.getStatusCode(), "No permission", "No client contact access permission");
            }
            if (!talentConfidentialService.confidentialTalentViewAble(lTalentId)) {
                return output;
            }
            List<TalentResumeDTO> talentResumeDTO = getTalentResumeDTO(lTalentId);
            output.setResumeInfo(talentResumeDTO);
            if(talentResumeDTO.isEmpty()) {
                TalentDTOV3 talentData = talentService.findTalentById(lTalentId, true);
                //租户候选人设置为true 便于前端判断
                talentData.setPurchased(true);
                output.setTalentData(JSONUtil.toJsonStr(talentData));
            }
        }

        if(jobId != null) {
            RecommendFeedback dto = new RecommendFeedback();
            dto.setTalentId(talentId);
            dto.setJobId(jobId);
            dto.setReason(RecommendFeedbackReason.GET_TALENT_RESUME);
            talentServiceV3.recordTalentJobRecommend(dto);
        }
        return output;
    }

    private List<TalentResumeDTO> getTalentResumeDTO(Long talentId) {
        List<TalentResumeDTO> talentResumeDTOS = new ArrayList<>();
        List<TalentResumeRelation> list = talentResumeRelationRepository.findAllByTalentId(talentId);
        if (CollUtil.isNotEmpty(list)) {
            if(!talentService.hasRemain(talentId)) {
                throw new CustomParameterizedException(Status.TOO_MANY_REQUESTS.getStatusCode(), "No limit", "Today's browsing limit has been reached");
            }
            List<Long> resumeIdList = list.stream().map(TalentResumeRelation::getResumeId).collect(Collectors.toList());
            List<Resume> resumeList = resumeRepository.findAllByIdIn(resumeIdList);
            for (TalentResumeRelation talentResumeRelation : list) {
                for (Resume resume : resumeList) {
                    if (talentResumeRelation.getResumeId().equals(resume.getId())) {
                        ImagesInfoDTO imagesInfoDTO = getImagesLinks(resume);
                        TalentResumeDTO talentResumeDTO = generateTalentResumeDTO(talentResumeRelation, resume, imagesInfoDTO);
                        talentResumeDTOS.add(talentResumeDTO);
                        break;
                    }
                }
            }
        }
        return talentResumeDTOS;
    }

    @Resource
    private TalentServiceV3 talentServiceV3;

    @Override
    public List<TalentResumeDTO> findAllWithoutPortraitByTalentId(Long talentId) {
        List<TalentResumeDTO> talentResumeDtos = new ArrayList<>();
        List<TalentResumeRelation> list = talentResumeRelationRepository.findAllByTalentId(talentId);
        if (CollUtil.isNotEmpty(list)) {
            List<Long> resumeIdList = list.stream().map(TalentResumeRelation::getResumeId).collect(Collectors.toList());
            List<TalentResumeBindSubmitToJob> submitToJobs = submitToJobRepository.findBindSubmitToJobByTalent(talentId);
            Map<Long, List<TalentResumeBindSubmitToJob>> resumeRelationMap = submitToJobs.stream().filter(p -> p.getTalentResumeRelationId() != null)
                    .collect(Collectors.groupingBy(TalentResumeBindSubmitToJob::getTalentResumeRelationId));

            List<Resume> resumeList = resumeRepository.findAllByIdIn(resumeIdList);
            Map<Long, Resume> resumeMap = resumeList.stream()
                    .collect(Collectors.toMap(Resume::getId, Function.identity()));
            for (TalentResumeRelation talentResumeRelation : list) {
                Resume resume = resumeMap.get(talentResumeRelation.getResumeId());
                if(resume == null) {
                    throw new CustomParameterizedException("Bind resume not exist.");
                }
                TalentResumeDTO talentResumeDTO = new TalentResumeDTO();
                ServiceUtils.myCopyProperties(talentResumeRelation, talentResumeDTO);
                talentResumeDTO.setUuid(resume.getUuid());
                talentResumeDTO.setText(resume.getText());
                talentResumeDTO.setImagesInfo(getImagesInfo(resume.getUuid()));
                List<TalentResumeBindSubmitToJob> submitToJobList = resumeRelationMap.get(talentResumeRelation.getId());
                if(submitToJobList != null) {
                    talentResumeDTO.setBindSubmitToJobList(submitToJobList);
                }
                // Users can download and delete resumes that they uploaded no matter they have the API permissions or not.
                talentResumeDTO.setHasPermission(SecurityUtils.getUserId().equals(talentResumeRelation.getPermissionUserId()));
                talentResumeDtos.add(talentResumeDTO);
            }
        }
        return talentResumeDtos;
    }

    @Override
    public List<TalentResumeDTO> findAllWithoutPortraitByTalentIds(List<Long> talentIds) {
        List<TalentResumeDTO> talentResumeDTOS = new ArrayList<>();
        List<TalentResumeRelation> list = talentResumeRelationRepository.findAllByTalentIdInAndStatusOrderByCreatedDateDesc(talentIds, CommonDataStatus.AVAILABLE);
        if (CollUtil.isNotEmpty(list)) {
            List<Long> resumeIdList = list.stream().map(TalentResumeRelation::getResumeId).collect(Collectors.toList());
            List<Resume> resumeList = resumeRepository.findAllByIdIn(resumeIdList);
            Map<Long, Resume> resumeMap = resumeList.stream().collect(Collectors.toMap(Resume::getId, Resume -> Resume));
            for (TalentResumeRelation talentResumeRelation : list) {
                if (resumeMap.containsKey(talentResumeRelation.getResumeId())) {
                    Resume resume = resumeMap.get(talentResumeRelation.getResumeId());
                    TalentResumeDTO talentResumeDTO = new TalentResumeDTO();
                    ServiceUtils.myCopyProperties(talentResumeRelation, talentResumeDTO);
                    talentResumeDTO.setUuid(resume.getUuid());
                    talentResumeDTO.setText(resume.getText());
                    talentResumeDTOS.add(talentResumeDTO);
                }
            }
        }
        return talentResumeDTOS;
    }

    @Override
    public TalentResumeDTO findByUuidAndTenantIdByCheckExistTalents(String uuid, Long tenantId) {
        return findTalentResume(uuid, tenantId, false);
    }

    @Override
    public TalentResumeDTO findByUuidAndTenantId(String uuid, Long tenantId) {
        return findTalentResume(uuid, tenantId, true);
    }

    private TalentResumeDTO findTalentResume(String uuid, Long tenantId, boolean includeDetails) {
        // 使用Optional避免null检查
        Resume resume = resumeRepository.findByUuid(uuid);
        if (resume == null) {
            return null;
        }

        Long resumeId = resume.getId();
        TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findByResumeIdAndTenantId(resumeId, tenantId);
        if (talentResumeRelation == null) {
            return null;
        }

        if (includeDetails) {
            ImagesInfoDTO imagesInfoDTO = getImagesLinks(resume);
            return generateTalentResumeDTO(talentResumeRelation, resume, imagesInfoDTO);
        } else {
            TalentResumeDTO dto = new TalentResumeDTO();
            ServiceUtils.myCopyProperties(talentResumeRelation, dto);
            return dto;
        }
    }

    @Resource
    private EnumCommonService enumCommonService;

    @Override
    public TalentResumeDTO generateTalentResumeDTO(TalentResumeRelation talentResumeRelation, Resume resume, ImagesInfoDTO imagesInfoDTO) {
        TalentResumeDTO talentResumeDTO = new TalentResumeDTO();
        ServiceUtils.myCopyProperties(talentResumeRelation, talentResumeDTO);
        talentResumeDTO.setUuid(resume.getUuid());
        talentResumeDTO.setText(resume.getText());
        talentResumeDTO.setImagesInfo(imagesInfoDTO);
        Optional<TalentV3> talentOpt = talentRepository.findById(talentResumeRelation.getTalentId());
        if(talentOpt.isPresent()) {
            TalentV3 talentV3 = talentOpt.get();
            talentResumeDTO.setTalentName(CommonUtils.formatFullNameWithBlankCheck(talentV3.getFirstName(), talentV3.getLastName()));
            TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(talentV3);
            List<TalentExperienceDTO> talentExperienceDTOS = TalentServiceImpl.sortTalentExperiences(talentDTOV3.getExperiences());
            if(talentExperienceDTOS != null && !talentExperienceDTOS.isEmpty()) {
                TalentExperienceDTO experienceDTO = talentExperienceDTOS.get(0);
                talentResumeDTO.setCompanyName(experienceDTO.getCompanyName());
                talentResumeDTO.setTitle(experienceDTO.getTitle());
            }
        }
        talentResumeDTO.setBindSubmitToJobList(submitToJobRepository.findBindSubmitToJobByTalentResumeRelationId(talentResumeRelation.getId()));
        talentResumeDTO.setHasPermission(SecurityUtils.getUserId().equals(talentResumeRelation.getPermissionUserId()));
        return talentResumeDTO;
    }

    @Override
    public List<Long> getTalentIdsByResumeDTOS(List<TalentResumeDTO> resumes) {
        List<Long> talentIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(resumes)) {
            for (TalentResumeDTO resume : resumes) {
                TalentResumeRelation talentResumeRelation = talentResumeRelationRepository.findByResumeIdAndTenantId(resume.getResumeId(), SecurityUtils.getTenantId());
                if (talentResumeRelation != null) {
                    talentIds.add(talentResumeRelation.getTalentId());
                }
            }
        }
        return talentIds;
    }

    @Override
    public TalentResumeDTO findLastByTalentId(Long talentId) {
        Resume resume = resumeRepository.findOneByTalentId(talentId, CommonDataStatus.AVAILABLE.toDbValue());
        if (ObjectUtil.isEmpty(resume)) {
            return null;
        }
        TalentResumeDTO talentResumeDTO = new TalentResumeDTO();
        talentResumeDTO.setUuid(resume.getUuid());
        talentResumeDTO.setTalentId(talentId);
        return talentResumeDTO;
    }
}
