package com.altomni.apn.talent.service.linkedinproject.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectTalent;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalent;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalentContact;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinProjectTalentRepository;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinTalentContactRepository;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinTalentRepository;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentContactDTO;
import com.altomni.apn.talent.service.linkedinproject.LinkedinTalentContactService;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinTalentContactVO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing LinkedinTalentContact.
 */
@Service
@Transactional
public class LinkedinTalentContactServiceImpl implements LinkedinTalentContactService {

    private final Logger log = LoggerFactory.getLogger(LinkedinTalentContactServiceImpl.class);

    private final LinkedinTalentContactRepository linkedinTalentContactRepository;

    private final LinkedinTalentRepository linkedinTalentRepository;

    private final LinkedinProjectTalentRepository linkedinProjectTalentRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    public LinkedinTalentContactServiceImpl(LinkedinTalentContactRepository linkedinTalentContactRepository,
                                            LinkedinTalentRepository linkedinTalentRepository,
                                            LinkedinProjectTalentRepository linkedinProjectTalentRepository,
                                            CommonApiMultilingualConfig commonApiMultilingualConfig,TalentApiPromptProperties talentApiPromptProperties) {
        this.linkedinTalentContactRepository = linkedinTalentContactRepository;
        this.linkedinTalentRepository = linkedinTalentRepository;
        this.linkedinProjectTalentRepository = linkedinProjectTalentRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
    }

    private void verifyTenant(Long tenantId) {
        if (!SecurityUtils.isCurrentTenant(tenantId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYTENANT_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    @Override
    public List<LinkedinTalentContactVO> replace(String linkedinTalentId, List<LinkedinTalentContactDTO> linkedinTalentContacts) {
        if (CollectionUtils.isEmpty(linkedinTalentContacts)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_REPLACE_LINKEDINTALENTCONTACTSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

//        LinkedinTalent linkedinTalent = linkedinTalentRepository.findById(linkedinTalentId).orElse(null);
//        if (linkedinTalent == null) {
//            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_REPLACE_LINKEDINTALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
//        }
//        verifyTenant(linkedinTalent.getTenantId());

        List<LinkedinProjectTalent> linkedinProjectTalents = linkedinProjectTalentRepository.findAllByLinkedinTalentIdAndTenantId(linkedinTalentId, SecurityUtils.getTenantId());

        linkedinTalentContactRepository.deleteAllByLinkedinTalentIdAndTenantId(linkedinTalentId, SecurityUtils.getTenantId());

        List<LinkedinTalentContact> replaceLinkedinTalentContactList = linkedinTalentContacts.stream().map(LinkedinTalentContact::fromLinkedinTalentContactDTO).collect(Collectors.toList());
        replaceLinkedinTalentContactList.forEach(linkedinTalentContact -> linkedinTalentContact.setLinkedinTalentId(linkedinTalentId));

        linkedinProjectTalents.forEach(pt -> pt.setHasContactInfo(true));
        linkedinProjectTalentRepository.saveAll(linkedinProjectTalents);

        return linkedinTalentContactRepository.saveAll(replaceLinkedinTalentContactList).stream().map(LinkedinTalentContactVO::fromLinkedinTalentContact).collect(Collectors.toList());
    }

    @Override
    public List<LinkedinTalentContactVO> findAll(String linkedinTalentId) {
//        LinkedinTalent linkedinTalent = linkedinTalentRepository.findById(linkedinTalentId).orElse(null);
//        if (linkedinTalent == null) {
//            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_REPLACE_LINKEDINTALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
//        }
//        verifyTenant(linkedinTalent.getTenantId());
//        return linkedinTalentContactRepository.findAllByLinkedinTalentId(linkedinTalentId).stream().map(LinkedinTalentContactVO::fromLinkedinTalentContact).collect(Collectors.toList());

        return linkedinTalentContactRepository.findAllByLinkedinTalentIdAndTenantId(linkedinTalentId, SecurityUtils.getTenantId()).stream().map(LinkedinTalentContactVO::fromLinkedinTalentContact).collect(Collectors.toList());
    }

    @Override
    public void deleteAllContactsByLinkedinTalentId(String linkedinTalentId) {
//        LinkedinTalent linkedinTalent = linkedinTalentRepository.findById(linkedinTalentId).orElse(null);
//        if (linkedinTalent == null) {
//            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_REPLACE_LINKEDINTALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
//        }
        List<LinkedinProjectTalent> linkedinProjectTalents = linkedinProjectTalentRepository.findAllByLinkedinTalentIdAndTenantId(linkedinTalentId, SecurityUtils.getTenantId());
        if (CollectionUtils.isEmpty(linkedinProjectTalents)) {
            return;
        }

        linkedinTalentContactRepository.deleteAllByLinkedinTalentIdAndTenantId(linkedinTalentId, SecurityUtils.getTenantId());
    }
}
