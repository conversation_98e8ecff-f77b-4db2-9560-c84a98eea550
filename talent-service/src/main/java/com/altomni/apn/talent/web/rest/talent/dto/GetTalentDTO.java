package com.altomni.apn.talent.web.rest.talent.dto;

import com.altomni.apn.common.dto.company.ClientContactDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetTalentDTO extends TalentDTOV3 {

    private Long clientContactCompanyId;

    private List<ClientContactDTO> companyAffiliations;

    private UserPersonalizationConfig personalizationConfig;

    /**
     * 关键联系人需求增加
     */
    private Boolean isKeyContact;

}
