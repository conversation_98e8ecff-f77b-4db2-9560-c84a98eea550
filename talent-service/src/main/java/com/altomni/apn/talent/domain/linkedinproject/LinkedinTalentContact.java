package com.altomni.apn.talent.domain.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.ContactTypeConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentContactDTO;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * A LinkedinTalentContact.
 */
@Entity
@Table(name = "linkedin_talent_contact")
public class LinkedinTalentContact extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -7571338464020633550L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "linkedin_talent_id")
    private String linkedinTalentId;

    @NotNull
    @Convert(converter = ContactTypeConverter.class)
    @Column(name = "contact_type", nullable = false)
    private ContactType type;

    @NotNull
    @Column(name = "contact", nullable = false)
    private String contact;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLinkedinTalentId() {
        return linkedinTalentId;
    }

    public void setLinkedinTalentId(String linkedinTalentId) {
        this.linkedinTalentId = linkedinTalentId;
    }

    public ContactType getType() {
        return type;
    }

    public void setType(ContactType type) {
        this.type = type;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinTalentContact linkedinTalentContact = (LinkedinTalentContact) o;
        if (linkedinTalentContact.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinTalentContact.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinTalentContact{" +
            "id=" + id +
            ", linkedinTalentId='" + linkedinTalentId + '\'' +
            ", type=" + type +
            ", contact='" + contact + '\'' +
            '}';
    }

    public static LinkedinTalentContact fromLinkedinTalentContactDTO(LinkedinTalentContactDTO LinkedinTalentContactDTO) {
        LinkedinTalentContact linkedinTalentContact = new LinkedinTalentContact();
        ServiceUtils.myCopyProperties(LinkedinTalentContactDTO, linkedinTalentContact);
        return linkedinTalentContact;
    }
}
