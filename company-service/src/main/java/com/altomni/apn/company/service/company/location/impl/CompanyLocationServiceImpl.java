package com.altomni.apn.company.service.company.location.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.location.CompanyLocationRepository;
import com.altomni.apn.company.repository.business.SalesLeadClientContactRepository;
import com.altomni.apn.company.repository.talent.TalentCurrentLocationCompanyBriefRepository;
import com.altomni.apn.company.service.company.location.CompanyLocationService;
import com.altomni.apn.company.service.dto.location.CompanyLocationDTO;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.altomni.apn.company.vo.location.CompanyLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Service
@Slf4j
public class CompanyLocationServiceImpl implements CompanyLocationService {

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private CompanyLocationRepository companyLocationRepository;

    @Resource
    private EsFillerCompanyService esFillerCompanyService;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private TalentCurrentLocationCompanyBriefRepository talentCurrentLocationCompanyBriefRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    private final String KEY_LOCATIONS = "locations";

    @Override
    public CompanyLocationVO createCompanyLocation(CompanyLocationDTO locationDTO) {
        Company existCompany = companyRepository.findById(locationDTO.getCompanyId()).orElseThrow(()->
         new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.LOCATION_COMMON_COMPANYNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()))
        );

        if (!existCompany.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.LOCATION_COMMON_COMPANYNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
//        checkDuplicatedCompanyLocation(locationDTO);

        CompanyLocation companyLocation = companyLocationRepository.save(CompanyLocation.fromCompanyLocationDTO(locationDTO));
        existCompany.setLastModifiedDate(Instant.now());
        companyRepository.save(existCompany);
        return CompanyLocationVO.fromCompanyLocation(companyLocation);
    }

    @Override
    public List<CompanyLocationVO> queryCompanyLocationList(Long companyId) {
        Company existCompany = companyRepository.findById(companyId).orElseThrow(()->
                new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.LOCATION_COMMON_COMPANYNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()))
        );

        if (!existCompany.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.LOCATION_COMMON_COMPANYNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        List<CompanyLocation> companyLocationList = companyLocationRepository.findAllByAccountCompanyId(companyId);
        return companyLocationList.stream().map(CompanyLocationVO::fromCompanyLocation).collect(Collectors.toList());
    }

    @Override
    public LocationDTO queryCompanyLocation(Long id) {
        CompanyLocation companyLocation = companyLocationRepository.findById(id).orElse(null);
        LocationDTO locationDTO = null;
        if (companyLocation != null) {
            locationDTO = new LocationDTO();
            LocationDTO companyLocationDto = JSONUtil.toBean(companyLocation.getOriginalLoc(), LocationDTO.class);
            ServiceUtils.myCopyProperties(companyLocationDto, locationDTO);
            locationDTO.setId(companyLocation.getId());
        }
        return locationDTO;
    }

    @Override
    public List<LocationDTO> checkLocationCompliance(@RequestBody LocationDTO locationDTO) {
        HttpResponse response = esFillerCompanyService.splitLocation(JsonUtil.toJson(locationDTO));
        if (response != null && response.getCode() == HttpStatus.OK.value()) {
            JSONObject locationObj = JSONUtil.parseObj(response.getBody());
            if (locationObj.containsKey(KEY_LOCATIONS)) {
                return JSONUtil.toList(JSONUtil.parseArray(locationObj.get(KEY_LOCATIONS)), LocationDTO.class);
            } else {
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }
}
