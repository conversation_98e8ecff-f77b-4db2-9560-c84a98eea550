package com.altomni.apn.report.service.e5;

import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.RecruitingKpiApplicationBaseDetailVO;
import com.altomni.apn.report.domain.vo.RecruitingKpiTalentNoteDetailVO;
import com.altomni.apn.report.domain.vo.e5.EmailDetailReportVO;
import com.altomni.apn.report.domain.vo.e5.TalentNoteDetailReportVO;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.report.dto.e5.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface UserAdoptionReportDetailsService {

    Map<Long, int[]> getCallReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    List<VoipDetailReportVO> getCallDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    List<VoipDetailReportVO> getCallDetailReportByTeamView(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    void downloadCallDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

    Map<Long, UserAdoptionReportApplicationStatsDTO> getApplicationStatsReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    Page<? extends RecruitingKpiApplicationBaseDetailVO> getApplicationDetailsReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, ReportApplicationStatus applicationDetailReportType, Pageable pageable);

    void downloadApplicationDetailsReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, ReportApplicationStatus applicationDetailReportType, HttpServletResponse response);

    Page<?> getNoteDetailReport(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    void downloadNoteDetailReport(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

    List<EmailDetailReportVO> getEmailDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO);

    void downloadEmailDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

    List<UserAdoptionReportCrmNoteStatsDTO> getCrmNoteReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

}
