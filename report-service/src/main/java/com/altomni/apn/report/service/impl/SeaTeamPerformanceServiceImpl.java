package com.altomni.apn.report.service.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.SeaTeamPerformanceExcelVO;
import com.altomni.apn.report.domain.vo.SeaTeamPerformanceVO;
import com.altomni.apn.report.dto.SeaTeamPerformanceSearchDto;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.repository.SeaTeamPerformanceRepository;
import com.altomni.apn.report.service.ReportPipelineService;
import com.altomni.apn.report.service.SeaTeamPerformanceService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class SeaTeamPerformanceServiceImpl implements SeaTeamPerformanceService {

    private final SeaTeamPerformanceRepository seaTeamPerformanceRepository;
    private final UserService userService;
    private final CachePermission cachePermission;
    private final InitiationService initiationService;
    private final ReportRepository reportRepository;
    private final EnumCommonService enumCommonService;
    private final ReportPipelineService reportPipelineService;
    @Qualifier("commonThreadPool")
    private final Executor executor;

    @Override
    public List<SeaTeamPerformanceVO> searchSeaTeamPerformance(SeaTeamPerformanceSearchDto searchDto) {
        log.info("[apn @{}] search sea team performance report by user, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("sea team performance report by user");

        stopWatch.start("[1] search permission data task");
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        log.info("search data for users: {}", JSONUtil.toJsonStr(searchDto.getUserIds()));
        stopWatch.stop();
        stopWatch.start("[2] search data task");
        Set<Long> filterUserIds = new HashSet<>();
        Set<Long> filterTeamIds = new HashSet<>();
        if (!searchDto.getUserIds().isEmpty()) {
            filterUserIds.addAll(searchDto.getUserIds());
        }
        if (!searchDto.getTeamIds().isEmpty()) {
            filterTeamIds.addAll(searchDto.getTeamIds());
        }
        boolean isValid = reportPipelineService.applyDataPermission(filterUserIds, filterTeamIds);
        if (!isValid) {
            return Collections.emptyList();
        }
        CompletableFuture<Map<Long, Integer>> newJobsMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryNewJobsCount(searchDto, teamDTO), executor);
        CompletableFuture<Map<Long, Integer>> assignedJobsMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryAssignedJobsCount(searchDto, teamDTO), executor);
        CompletableFuture<Map<Long, Integer>> filledMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryFilledCount(searchDto, teamDTO), executor);
        CompletableFuture<Map<Long, Integer>> submitToClientMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.querySubmitToClientCount(searchDto, filterUserIds, filterTeamIds), executor);
        CompletableFuture<Map<Long, Integer>> interviewedCountMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryInterviewedCount(searchDto, filterUserIds, filterTeamIds), executor);
        CompletableFuture<Map<Long, Double>> clientToOnboardAvgTimeMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryClientToOnboardAvgTime(searchDto, filterUserIds, filterTeamIds), executor);
        CompletableFuture<Map<Long, Double>> firstInterviewToOnboardAvgTimeMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryFirstInterviewToOnboardAvgTime(searchDto, filterUserIds, filterTeamIds), executor);
        CompletableFuture<Map<Long, List<SeaTeamPerformanceVO.InvoiceAmountVO>>> invoiceAmountMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryInvoiceAmount(searchDto, filterUserIds, filterTeamIds), executor);
        CompletableFuture<Map<Long, Integer>> invoicedStartCountMapFuture = CompletableFuture.supplyAsync(() -> seaTeamPerformanceRepository.queryInvoiceStartCount(searchDto, filterUserIds, filterTeamIds), executor);

        CompletableFuture.allOf(newJobsMapFuture, assignedJobsMapFuture, filledMapFuture, submitToClientMapFuture,
                interviewedCountMapFuture, clientToOnboardAvgTimeMapFuture, firstInterviewToOnboardAvgTimeMapFuture,
                invoiceAmountMapFuture, invoicedStartCountMapFuture).join();
        stopWatch.stop();
        stopWatch.start("[3] merge data task");
        Map<Long, Integer> newJobsMap = newJobsMapFuture.join();
        Map<Long, Integer> assignedJobsMap = assignedJobsMapFuture.join();
        Map<Long, Integer> filledMap = filledMapFuture.join();
        Map<Long, Integer> submitToClientMap = submitToClientMapFuture.join();
        Map<Long, Integer> interviewedCountMap = interviewedCountMapFuture.join();
        Map<Long, Double> clientToOnboardAvgTimeMap = clientToOnboardAvgTimeMapFuture.join();
        Map<Long, Double> firstInterviewToOnboardAvgTimeMap = firstInterviewToOnboardAvgTimeMapFuture.join();
        Map<Long, List<SeaTeamPerformanceVO.InvoiceAmountVO>> invoiceAmountMap = invoiceAmountMapFuture.join();
        Map<Long, Integer> invoicedStartCountMap = invoicedStartCountMapFuture.join();

        Set<Long> userIds = Stream.of(newJobsMap, assignedJobsMap, filledMap, submitToClientMap, interviewedCountMap,
                        clientToOnboardAvgTimeMap, firstInterviewToOnboardAvgTimeMap, invoiceAmountMap, invoicedStartCountMap)
                .flatMap(map -> map.keySet().stream()).collect(Collectors.toSet());
        if (userIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, UserUidNameDTO> usernameMap = userService.getAllUserNameByIdInOrTenant(userIds).getBody();
        Map<Long, SeaTeamPerformanceVO> resultMap = new HashMap<>();
        if (usernameMap == null || usernameMap.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Integer, EnumCurrency> currencyMap = enumCommonService.findAllEnumCurrency().stream()
                .collect(Collectors.toMap(EnumCurrency::getId, Function.identity()));
        usernameMap.forEach((userId, user) -> {
            if (Objects.isNull(user)) {
                return;
            }
            SeaTeamPerformanceVO vo = new SeaTeamPerformanceVO();
            vo.setUserId(userId);
            vo.setUserName(CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
            vo.setNewJobs(newJobsMap.getOrDefault(userId, 0));
            vo.setAssignedJobs(assignedJobsMap.getOrDefault(userId, 0));
            vo.setFilledJobs(filledMap.getOrDefault(userId, 0));
            vo.setSubmitToClientCount(submitToClientMap.getOrDefault(userId, 0));
            vo.setInterviewedCount(interviewedCountMap.getOrDefault(userId, 0));
            vo.setClientToOnboardAvgTime(clientToOnboardAvgTimeMap.getOrDefault(userId, 0.0));
            vo.setFirstInterviewOnboardAvgTime(firstInterviewToOnboardAvgTimeMap.getOrDefault(userId, 0.0));
            vo.setInvoiceAmount(invoiceAmountMap.getOrDefault(userId, Collections.emptyList()));
            vo.setInvoiceStartCount(invoicedStartCountMap.getOrDefault(userId, 0));
            vo.calculateAssignedJobToPlacement()
                    .calculateClintToInterviewRatio()
                    .calculateAvgPlacementFee()
                    .convertCurrency(currencyMap, searchDto.getCurrency());
            resultMap.put(userId, vo);
        });
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        List<SeaTeamPerformanceVO> resultList = resultMap.values().stream().toList();
        if (searchDto.getSort() == null || searchDto.getSort().getDirection() == null || searchDto.getSort().getProperty() == null) {
            return resultList;
        } else {
            return resultList.stream().sorted(comparator(searchDto)).toList();
        }
    }

    @Override
    public List<Map<String, Object>> searchSeaTeamPerformanceDetail(SeaTeamPerformanceSearchDto searchDto, String type) {
        if (searchDto.getUserIds().isEmpty()) {
            return Collections.emptyList();
        }
        log.info("[apn @{}] search sea team performance detail by user, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("sea team performance report by user");

        stopWatch.start("[1] search permission data task");
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        stopWatch.stop();
        stopWatch.start("[2] search data task");
        Set<Long> privateJobTeamIds = reportRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());

        Set<Long> filterUserIds = new HashSet<>();
        Set<Long> filterTeamIds = new HashSet<>();
        if (!searchDto.getUserIds().isEmpty()) {
            filterUserIds.addAll(searchDto.getUserIds());
        }
        if (!searchDto.getTeamIds().isEmpty()) {
            filterTeamIds.addAll(searchDto.getTeamIds());
        }
        boolean isValid = reportPipelineService.applyDataPermission(filterUserIds, filterTeamIds);
        if (!isValid) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> result = switch (type) {
            case "newJobs" -> seaTeamPerformanceRepository.queryNewJobsDetail(searchDto, teamDTO);
            case "assignedJobs" -> seaTeamPerformanceRepository.queryAssignedJobsDetail(searchDto, teamDTO);
            case "clientToOnboardAvgTime" ->
                    seaTeamPerformanceRepository.queryClientToOnboardDetail(searchDto, filterUserIds, filterTeamIds);
            case "firstInterviewOnboardAvgTime" ->
                    seaTeamPerformanceRepository.queryFirstInterviewToOnboardDetail(searchDto, filterUserIds, filterTeamIds);
            case "invoiceAmount", "avgPlacementFee" ->
                    seaTeamPerformanceRepository.queryInvoiceAmountDetail(searchDto, filterUserIds, filterTeamIds);
            default -> Collections.emptyList();
        };
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return result.stream().peek(map -> setPrivateJob(map, privateJobTeamIds)).toList();
    }

    private void setPrivateJob(Map<String, Object> map, Set<Long> privateJobTeamIds) {
        if (!map.containsKey("jobPTeamId")) {
            return;
        }
        Long jobId = Long.parseLong(map.get("jobPTeamId").toString());
        map.put("privateJob", privateJobTeamIds.contains(jobId));
    }

    @Override
    public void downloadSeaTeamPerformanceReport(SeaTeamPerformanceSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("sea team performance report download");
        stopWatch.start("[1] fetch data task");
        List<SeaTeamPerformanceVO> seaTeamPerformanceVOList = searchSeaTeamPerformance(searchDto);
        if (seaTeamPerformanceVOList == null || seaTeamPerformanceVOList.isEmpty()) {
            throw new CustomParameterizedException("no data found");
        }
        stopWatch.stop();
        stopWatch.start("[2] convert data task");
        Map<Integer, EnumCurrency> currencyMap = enumCommonService.findAllEnumCurrency().stream()
                .collect(Collectors.toMap(EnumCurrency::getId, Function.identity()));
        List<SeaTeamPerformanceExcelVO> excelRows = seaTeamPerformanceVOList.stream()
                .map(vo -> SeaTeamPerformanceExcelVO.from(searchDto, vo, currencyMap)).toList();
        stopWatch.stop();
        stopWatch.start("[3] export data task");
        String zoneTime = LocalDateTime.now(ZoneId.of(searchDto.getTimezone())).format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss"));
        ExcelUtil.downloadExcel(response, SeaTeamPerformanceExcelVO.class, new ArrayList<>(excelRows), "", "SEA_Team_Performance_Report_" + zoneTime + ".xlsx", true);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    private Comparator<SeaTeamPerformanceVO> comparator(SeaTeamPerformanceSearchDto searchDto) {
        if ("avgPlacementFee".equals(searchDto.getSort().getProperty())) {
            if (searchDto.getCurrency() == null) {
                return (v1, v2) -> 0;
            }
            if ("asc".equalsIgnoreCase(searchDto.getSort().getDirection())) {
                return Comparator.comparing(SeaTeamPerformanceVO::getAvgPlacementFee, Comparator.nullsLast(Comparator.naturalOrder()));
            } else {
                return Comparator.comparing(SeaTeamPerformanceVO::getAvgPlacementFee, Comparator.nullsLast(Comparator.reverseOrder()));
            }
        }
        Comparator<SeaTeamPerformanceVO> comparator = (vo1, vo2) -> switch (searchDto.getSort().getProperty()) {
            case "userName" -> vo1.getUserName().compareTo(vo2.getUserName());
            case "newJobs" -> Integer.compare(vo1.getNewJobs(), vo2.getNewJobs());
            case "assignedJobs" -> Integer.compare(vo1.getAssignedJobs(), vo2.getAssignedJobs());
            case "filledJobs" -> Integer.compare(vo1.getFilledJobs(), vo2.getFilledJobs());
            case "assignedJobToPlacement" ->
                    Double.compare(vo1.getAssignedJobToPlacement(), vo2.getAssignedJobToPlacement());
            case "submitToClientCount" -> Integer.compare(vo1.getSubmitToClientCount(), vo2.getSubmitToClientCount());
            case "clientToInterviewRatio" ->
                    Double.compare(vo1.getClientToInterviewRatio(), vo2.getClientToInterviewRatio());
            case "interviewedCount" -> Integer.compare(vo1.getInterviewedCount(), vo2.getInterviewedCount());
            case "clientToOnboardAvgTime" ->
                    Double.compare(vo1.getClientToOnboardAvgTime(), vo2.getClientToOnboardAvgTime());
            case "firstInterviewOnboardAvgTime" ->
                    Double.compare(vo1.getFirstInterviewOnboardAvgTime(), vo2.getFirstInterviewOnboardAvgTime());
            case "invoiceAmount" -> {
                double sum1 = vo1.getInvoiceAmount().stream()
                        .map(SeaTeamPerformanceVO.InvoiceAmountVO::getAmount)
                        .mapToDouble(BigDecimal::doubleValue)
                        .sum();
                double sum2 = vo2.getInvoiceAmount().stream()
                        .map(SeaTeamPerformanceVO.InvoiceAmountVO::getAmount)
                        .mapToDouble(BigDecimal::doubleValue)
                        .sum();
                yield Double.compare(sum1, sum2);
            }
            default -> 0;
        };
        if ("desc".equalsIgnoreCase(searchDto.getSort().getDirection())) {
            return comparator.reversed();
        } else {
            return comparator;
        }
    }

    private TeamDataPermissionRespDTO getPermissionDTOAndSetCommonParam(SeaTeamPerformanceSearchDto searchDto) {
        Long userId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        searchDto.setSearchUserId(userId);
        searchDto.setSearchTenantId(tenantId);
        TeamDataPermissionRespDTO teamDataPermission = getPermissionDto(userId, tenantId);
        // 数据权限 only self, 只能查询自己的数据
        if (teamDataPermission.getSelf()) {
            searchDto.setUserIds(Set.of(userId));
        }
        // 如果用户有私有职位的权限，查询该用户的私有职位
        if (teamDataPermission.isPrivateJobPermission()) {
            searchDto.setPrivateJobIds(new HashSet<>(reportRepository.getJobIdsForPrivateJob(searchDto.getSearchUserId(), teamDataPermission.getTeamIdForPrivateJob())));
        }
        return teamDataPermission;
    }

    private TeamDataPermissionRespDTO getPermissionDto(Long userId, Long tenantId) {
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(userId);
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateReportDataPermissionByUserId(tenantId, userId).getBody();
        }
        return teamDataPermission;
    }
}
