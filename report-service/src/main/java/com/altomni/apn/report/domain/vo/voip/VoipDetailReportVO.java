package com.altomni.apn.report.domain.vo.voip;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoipDetailReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String phoneNumber;

    private boolean wrongNumber;

    private Long talentId;

    private Long contactId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private Instant dialingTime;

    private Double talkMinutes;

    private Long callResult;

    private Long callType;

    private JSONObject AISummary;

    private String note;

    private Boolean privateJob;

    private Long userId;

    private String userName;

}
