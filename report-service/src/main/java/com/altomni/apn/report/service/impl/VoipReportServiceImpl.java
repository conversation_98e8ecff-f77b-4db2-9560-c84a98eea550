package com.altomni.apn.report.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.talent.TalentNoteDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.report.domain.vo.voip.VoipReportVO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.report.dto.voip.VoipDetailReportDTO;
import com.altomni.apn.report.service.VoipReportService;
import com.altomni.apn.report.service.job.JobService;
import com.altomni.apn.report.service.talent.TalentService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.report.service.voip.VoipService;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class VoipReportServiceImpl implements VoipReportService {

    private final VoipService voipService;

    private final TalentService talentService;

    private final JobService jobService;

    private final UserService userService;


    private static final Integer TEAM_DATA_SCOPE = 2;

    private static final Integer ALL_DATA_SCOPE = 99;

    public VoipReportServiceImpl(VoipService voipService, TalentService talentService, JobService jobService, UserService userService) {
        this.voipService = voipService;
        this.talentService = talentService;
        this.jobService = jobService;
        this.userService = userService;
    }

    private static <T extends Comparable<T>> Comparator<T> getNullHandlingComparator(Sort.Order order) {
        return Comparator.nullsLast(Comparator.naturalOrder());
    }

    private static Comparator<VoipReportVO> getVoipReportComparatorFromSort(Sort sort) {
        return sort.stream()
                .map(VoipReportServiceImpl::getVoipReportComparator)
                .filter(Objects::nonNull)
                .reduce(Comparator::thenComparing)
                .orElse((u1, u2) -> 0); // 如果没有排序规则，则不变
    }

    private static Comparator<VoipReportVO> getVoipReportComparator(Sort.Order order) {
        Comparator<VoipReportVO> comparator = null;

        switch (order.getProperty().toLowerCase()) {
            case "user" -> comparator = Comparator.comparing(VoipReportVO::getUserName, getNullHandlingComparator(order));
            case "totalcall" -> comparator = Comparator.comparing(VoipReportVO::getTotalCalls, getNullHandlingComparator(order));
            case "connects" -> comparator = Comparator.comparing(VoipReportVO::getConnects, getNullHandlingComparator(order));
            case "qualifiedconnects" -> comparator = Comparator.comparing(VoipReportVO::getQualifiedConnects, getNullHandlingComparator(order));
            case "connectrate" -> comparator = Comparator.comparing(VoipReportVO::getConnectRate, getNullHandlingComparator(order));
            case "totalcandidatescalled" -> comparator = Comparator.comparing(VoipReportVO::getTotalCandidateCalls, getNullHandlingComparator(order));
            case "totalcandidatesreached" -> comparator = Comparator.comparing(VoipReportVO::getTotalCandidateReached, getNullHandlingComparator(order));
            case "totaltalkminutes" -> comparator = Comparator.comparing(VoipReportVO::getTotalTalkMinutes, getNullHandlingComparator(order));
        }
        return (comparator != null && order.isDescending()) ? comparator.reversed() : comparator;
    }

    public static Comparator<VoipDetailReportVO> getVoipReportDetailComparatorFromSort(Sort sort) {
        return sort.stream()
                .map(order -> getVoipReportDetailComparator(order))
                .filter(Objects::nonNull)
                .reduce(Comparator::thenComparing)
                .orElse((u1, u2) -> 0); // 如果没有排序规则，则不变
    }

    public static Comparator<VoipDetailReportVO> getVoipReportDetailComparator(Sort.Order order) {
        Comparator<VoipDetailReportVO> comparator = null;

        switch (order.getProperty().toLowerCase()) {
            case "talentname" -> {
                Collator collator = Collator.getInstance(Locale.CHINA); // 中文拼音排序
                comparator = Comparator.comparing(
                        VoipDetailReportVO::getTalentName,
                        Comparator.nullsLast(collator)
                );
            }

            case "userNmae" ->  {
                Collator collator = Collator.getInstance(Locale.CHINA); // 中文拼音排序
                comparator = Comparator.comparing(
                        VoipDetailReportVO::getUserName,
                        Comparator.nullsLast(collator)
                );
            }
            case "talentid" -> comparator = Comparator.comparing(VoipDetailReportVO::getTalentId, getNullHandlingComparator(order));
            case "talkminutes" -> comparator = Comparator.comparing(VoipDetailReportVO::getTalkMinutes, getNullHandlingComparator(order));
            case "dialingtime" -> comparator = Comparator.comparing(VoipDetailReportVO::getDialingTime, getNullHandlingComparator(order));
            case "callresult" -> comparator = Comparator.comparing(voip -> Optional.ofNullable(voip.getCallResult()).orElse(order.isDescending() ? Long.MIN_VALUE : Long.MAX_VALUE), Comparator.naturalOrder());
            case "calltype" -> comparator = Comparator.comparing(voip -> Optional.ofNullable(voip.getCallType()).orElse(order.isDescending() ? Long.MIN_VALUE : Long.MAX_VALUE), Comparator.naturalOrder());

        }
        return (comparator != null && order.isDescending()) ? comparator.reversed() : comparator;
    }

    private void checkValidFilter(VoipReportDTO voipReportDTO) {
        if (voipReportDTO.getStartTime() == null && voipReportDTO.getEndTime() == null) {
            throw new IllegalArgumentException("Either startTime or endTime must be provided.");
        }
        if (voipReportDTO.getStartTime() != null && voipReportDTO.getEndTime() != null && voipReportDTO.getStartTime().isAfter(voipReportDTO.getEndTime())) {
            throw new IllegalArgumentException("startTime must be before endTime.");
        }
    }

    private Boolean checkValidPhoneTranscriptions(PhoneTranscription phoneTranscription) {
        return phoneTranscription.getTranscriptions() != null || (phoneTranscription.getTotalTime() != null && phoneTranscription.getTotalTime() > 0);
    }

    private void collectUserIds(PermissionTeamTreeDTO teamTree, Set<Long> userIds) {
        if (teamTree == null) {
            return;
        }
        if (teamTree.getData() != null) {
            for (PermissionTeamUserDTO user : teamTree.getData()) {
                if (user != null && user.getId() != null) {
                    userIds.add(user.getId());
                }
            }
        }

        // 递归遍历子节点
        if (teamTree.getChildren() != null) {
            for (PermissionTeamTreeDTO child : teamTree.getChildren()) {
                collectUserIds(child, userIds);
            }
        }
    }

    private Set<Long> checkReportDataPermission(Long userId, Set<Long> allUserIds, VoipReportDTO voipReportDTO) throws ExecutionException, InterruptedException {
        if(SecurityUtils.isAdmin()) return allUserIds;
        PermissionUserTeamPermissionVM permissions = userService.getDataPermissionsByUserId(userId).getBody();
        Set<Long> availableUserIds = new HashSet<>();
        availableUserIds.add(userId);
        if(permissions != null) {
            PermissionUserTeamPermissionVM.PermissionDetail permissionDetail = permissions.getReportPermission();
            if(permissionDetail != null) {
                if(permissionDetail.getDataScope().equals(TEAM_DATA_SCOPE)) {
                    boolean isPrimary = false;
                    if(voipReportDTO.getTeamIdList() == null && voipReportDTO.getUserIdList() == null) {
                        isPrimary = true;
                    }
                    List<PermissionTeamTreeDTO> permissionTeamTrees = userService.getTeamsWithPermissionAndUserByType(Module.REPORT, isPrimary).getBody();
                    if(permissionTeamTrees != null) {
                        for (PermissionTeamTreeDTO teamTree : permissionTeamTrees) {
                            collectUserIds(teamTree, availableUserIds);
                        }
                    }
                }
                if(permissionDetail.getDataScope().equals(ALL_DATA_SCOPE)) {
                    availableUserIds.addAll(allUserIds);
                }
            }
        }
        return availableUserIds;
    }

    @Override
    public List<VoipReportVO> getVoipReport(VoipReportDTO voipReportDTO, Sort sort) throws ExecutionException, InterruptedException {
        checkValidFilter(voipReportDTO);
        voipReportDTO.setTenantId(SecurityUtils.getTenantId());
        List<VoipReportVO> reports;
        Map<Long, VoipReportVO> reportMap = new HashMap<>();
        Map<Long, Map<Long, VoipReportVO>> candidateReportMap = new HashMap<>();
        List<VoipContactDTO> voipContacts = voipService.findAllByVoipReport(voipReportDTO).getBody();
        List<String> phoneCallIds = voipContacts != null ? voipContacts.stream().map(VoipContactDTO::getPhoneCallId).toList() : new ArrayList<>();
        List<PhoneTranscription> phoneTranscriptionRes = voipService.findAllPhoneTranscriptionByPhoneCallIds(new HashSet<>(phoneCallIds)).getBody();
        Map<String, PhoneTranscription> phoneTranscriptions = phoneTranscriptionRes != null ? phoneTranscriptionRes.stream().collect(Collectors.toMap(PhoneTranscription::getPhoneCallId, phoneTranscription -> phoneTranscription)) : new HashMap<>();
        if(voipContacts != null) {
            for(VoipContactDTO contact : voipContacts) {
                //if the user didn't record on the report
                if(reportMap.get(contact.getUserId()) == null) {
                    VoipReportVO report = new VoipReportVO();
                    report.setUserId(contact.getUserId());
                    report.setUserName(contact.getUserName());
                    report.setTotalCalls(1);
                    VoipReportVO candidateReport = new VoipReportVO();
                    Map<Long, VoipReportVO> talentCandidateReport = new HashMap<>();
                    candidateReport.setTotalCandidateCalls(1);
                    if(phoneTranscriptions.get(contact.getPhoneCallId()) != null) {
                        PhoneTranscription phoneTranscription = phoneTranscriptions.get(contact.getPhoneCallId());
                        if(checkValidPhoneTranscriptions(phoneTranscription)) {
                            report.setConnects(1);
                            candidateReport.setTotalCandidateReached(1);
                        }
                        if(phoneTranscription.getTotalTime() != null) {
                            if(phoneTranscription.getTotalTime() > 60) report.setQualifiedConnects(1);
                            report.setTotalTalkMinutes(Math.ceil((phoneTranscription.getTotalTime().doubleValue()) / 60));
                        }
                    }
                    talentCandidateReport.put(contact.getTalentId(), candidateReport);
                    candidateReportMap.put(contact.getUserId(), talentCandidateReport);
                    report.setTotalCandidateCalls(candidateReport.getTotalCandidateCalls());
                    report.setTotalCandidateReached(candidateReport.getTotalCandidateReached());
                    reportMap.put(contact.getUserId(), report);
                }
                //if the user already record on the report
                else {
                    VoipReportVO report = reportMap.get(contact.getUserId());
                    report.setTotalCalls(report.getTotalCalls() + 1);
                    if(phoneTranscriptions.get(contact.getPhoneCallId()) != null) {
                        PhoneTranscription phoneTranscription = phoneTranscriptions.get(contact.getPhoneCallId());
                        if(checkValidPhoneTranscriptions(phoneTranscription)) {
                            report.setConnects(report.getConnects() + 1);
                        }
                        if(phoneTranscription.getTotalTime() != null) {
                            if(phoneTranscription.getTotalTime() > 60) report.setQualifiedConnects(report.getQualifiedConnects() + 1);
                            report.setTotalTalkMinutes(report.getTotalTalkMinutes() + Math.ceil((phoneTranscription.getTotalTime().doubleValue()) / 60));
                        }
                    }
                    VoipReportVO candidateReport;
                    //if the user-candidate report exist on the report
                    if(candidateReportMap.get(contact.getUserId()) != null && candidateReportMap.get(contact.getUserId()).get(contact.getTalentId()) != null) {
                        candidateReport = candidateReportMap.get(contact.getUserId()).get(contact.getTalentId());
                        if(candidateReport.getTotalCandidateCalls() == 0) {
                            report.setTotalCandidateCalls(report.getTotalCandidateCalls() + 1);
                            candidateReport.setTotalCandidateCalls(candidateReport.getTotalCandidateCalls() + 1);
                        }
                        if(phoneTranscriptions.get(contact.getPhoneCallId()) != null) {
                            PhoneTranscription phoneTranscription = phoneTranscriptions.get(contact.getPhoneCallId());
                            if((phoneTranscription.getTranscriptions() != null || (phoneTranscription.getTotalTime() != null && phoneTranscription.getTotalTime() > 0)) && candidateReport.getTotalCandidateReached() == 0) {
                                report.setTotalCandidateReached(report.getTotalCandidateReached() + 1);
                                candidateReport.setTotalCandidateReached(candidateReport.getTotalCandidateReached() + 1);
                            }
                        }
                    }
                    else {
                        candidateReport = new VoipReportVO();
                        if(candidateReport.getTotalCandidateCalls() == 0) {
                            report.setTotalCandidateCalls(report.getTotalCandidateCalls() + 1);
                            candidateReport.setTotalCandidateCalls(1);
                        }
                        if(phoneTranscriptions.get(contact.getPhoneCallId()) != null) {
                            PhoneTranscription phoneTranscription = phoneTranscriptions.get(contact.getPhoneCallId());
                            if((checkValidPhoneTranscriptions(phoneTranscription)) && candidateReport.getTotalCandidateReached() == 0) {
                                report.setTotalCandidateReached(report.getTotalCandidateReached()  + 1);
                                candidateReport.setTotalCandidateReached(1);
                            }
                        }
                    }
                    candidateReportMap.get(contact.getUserId()).put(contact.getTalentId(), candidateReport);
                }
            }
        }
        reports = new ArrayList<>(reportMap.values().stream().peek(voipReportVO -> {
            voipReportVO.setConnectRate(BigDecimal.valueOf((double) voipReportVO.getConnects() / voipReportVO.getTotalCalls() * 100)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue()
            );
        }).toList());
        //report users who doesn't hav any voip contact call record, set these
        Set<Long> allUserIds = new HashSet<>();
        List<UserBriefDTO> users = new ArrayList<>();
        try {
            ResponseEntity<List<UserBriefDTO>> res = userService.getAllBriefUsersByTenantId(SecurityUtils.getTenantId());
            users = res.getBody();
        }
        catch (Exception e) {
            log.error("voip report service get all brief users error: {}, stacktrace: {}", e.getMessage(), e.getStackTrace());
        }
        Set<Long> recordUserIds = reports.stream().map(VoipReportVO::getUserId).collect(Collectors.toSet());
        if(voipReportDTO.getUserIdList() != null && !voipReportDTO.getUserIdList().isEmpty()) {
            allUserIds.addAll(voipReportDTO.getUserIdList());
        }
        if(voipReportDTO.getTeamIdList() != null && !voipReportDTO.getTeamIdList().isEmpty()) {
            try {
                ResponseEntity<Set<Long>> res = userService.getAllTeamUserIdsByPermissionTeamIdIn(new HashSet<>(voipReportDTO.getTeamIdList()));
                Set<Long> teamUserIds = res.getBody();
                if(teamUserIds != null) allUserIds.addAll(teamUserIds);
            }
            catch (Exception e) {
                log.error("voip report service get userIds by teams error: {}, stacktrace: {}", e.getMessage(), e.getStackTrace());
            }
        }
        if(voipReportDTO.getUserIdList() == null && voipReportDTO.getTeamIdList() == null) {
            if(users != null) allUserIds.addAll(users.stream().map(UserBriefDTO::getId).collect(Collectors.toSet()));
        }
        allUserIds.removeAll(recordUserIds);
        Map<Long, UserBriefDTO> userMap = users != null ? users.stream().collect(Collectors.toMap(UserBriefDTO::getId, userBriefDTO -> userBriefDTO)) : new HashMap<>();
        for(Long userId : allUserIds) {
            if(userMap.get(userId) != null) {
                VoipReportVO voipReportVO = new VoipReportVO();
                voipReportVO.setUserId(userId);
                voipReportVO.setUserName(userMap.get(userId).getFullName());
                reports.add(voipReportVO);
            }
        }
        Set<Long> availableUserIds = checkReportDataPermission(SecurityUtils.getUserId(), users != null ? users.stream().map(UserBriefDTO::getId).collect(Collectors.toSet()) : new HashSet<>(), voipReportDTO);
        if(sort != null) reports.sort(getVoipReportComparatorFromSort(sort));
        reports = reports.stream().filter(voipReportVO -> availableUserIds.contains(voipReportVO.getUserId())).collect(Collectors.toList());
        return reports;
    }

    @Override
    public List<VoipDetailReportVO> getVoipDetailReport(VoipDetailReportDTO voipDetailReportDTO, Sort sort) throws ExecutionException, InterruptedException {
        List<UserBriefDTO> users = new ArrayList<>();
        try {
            ResponseEntity<List<UserBriefDTO>> res = userService.getAllBriefUsersByTenantId(SecurityUtils.getTenantId());
            users = res.getBody();
        }
        catch (Exception e) {
            log.error("voip report service get all brief users error: {}, stacktrace: {}", e.getMessage(), e.getStackTrace());
        }
//        Set<Long> allUserIds = users != null ? users.stream().map(UserBriefDTO::getId).collect(Collectors.toSet()) : new HashSet<>();
//        Set<Long> availableUserIds = checkReportDataPermission(SecurityUtils.getUserId(), allUserIds);
//        if(!availableUserIds.contains(voipDetailReportDTO.getUserId())) throw new CustomParameterizedException("You don't have access permission to get the user's call detail report.");
        List<VoipDetailReportVO> voipDetailReports;
        VoipReportDTO reportDTO = new VoipReportDTO();
        reportDTO.setUserIdList(List.of(voipDetailReportDTO.getUserId()));
        reportDTO.setStartTime(voipDetailReportDTO.getStartTime());
        reportDTO.setEndTime(voipDetailReportDTO.getEndTime());
        reportDTO.setTimeZone(voipDetailReportDTO.getTimeZone());
        reportDTO.setTenantId(SecurityUtils.getTenantId());
        reportDTO.setCallResultList(voipDetailReportDTO.getCallResultList());
        reportDTO.setCallTypeList(voipDetailReportDTO.getCallTypeList());
        List<VoipContactDTO> voipContacts = voipService.findAllByVoipReport(reportDTO).getBody();
        List<String> phoneCallIds = voipContacts.stream().map(VoipContactDTO::getPhoneCallId).toList();
        List<Long> jobIds = voipContacts.stream().map(VoipContactDTO::getJobId).toList();
        Set<Long> talentIds = voipContacts.stream().map(VoipContactDTO::getTalentId).collect(Collectors.toSet());
        Map<Long, JobBriefDTO> jobs = !jobIds.isEmpty() ? jobService.getBriefJobListByIds(jobIds).getBody().stream().collect(Collectors.toMap(JobBriefDTO::getId, jobBriefDTO -> jobBriefDTO)) : new HashMap<>();
        Map<String, TalentNoteDTO> talentNotes = !phoneCallIds.isEmpty() ? talentService.getAllCallCandidateNotesByVoipPhoneCallIds(phoneCallIds).getBody().stream().collect(Collectors.toMap(TalentNoteDTO::getPhoneCallId, talentNoteDTO -> talentNoteDTO)) : new HashMap<>();
        Map<String, PhoneTranscription> phoneTranscriptions = !phoneCallIds.isEmpty() ? voipService.findAllPhoneTranscriptionByPhoneCallIds(new HashSet<>(phoneCallIds)).getBody().stream().collect(Collectors.toMap(PhoneTranscription::getPhoneCallId, phoneTranscription -> phoneTranscription)) : new HashMap<>();
        Map<Long, TalentBriefVO> talents = !talentIds.isEmpty() ? talentService.findBriefTalentsByTalentIds(talentIds).getBody().stream().collect(Collectors.toMap(TalentBriefVO::getId, talentBriefVO -> talentBriefVO)) : new HashMap<>();
        Set<Long> privateJobIds = !jobIds.isEmpty() ? jobService.findPrivateJobIds(jobIds):null;
        voipDetailReports = voipContacts.stream().map(voipContactDTO -> {
            VoipDetailReportVO voipDetailReportVO = new VoipDetailReportVO();
            voipDetailReportVO.setPhoneNumber(voipContactDTO.getPhoneNumber());
            voipDetailReportVO.setWrongNumber(BooleanUtils.isTrue(voipContactDTO.getIsWrongNumber()));
            voipDetailReportVO.setTalentId(voipContactDTO.getTalentId());
            voipDetailReportVO.setTalentName(voipContactDTO.getTalentId() != null ? talents.getOrDefault(voipContactDTO.getTalentId(),null).getFullName() : null);
            voipDetailReportVO.setJobId(voipContactDTO.getJobId());
            if(voipContactDTO.getJobId() != null) voipDetailReportVO.setJobTitle(jobs.get(voipContactDTO.getJobId()) != null ? jobs.get(voipContactDTO.getJobId()).getTitle() : null);
            voipDetailReportVO.setDialingTime(voipContactDTO.getCreatedDate());
            Long totalTime = phoneTranscriptions.get(voipContactDTO.getPhoneCallId()) != null ? phoneTranscriptions.get(voipContactDTO.getPhoneCallId()).getTotalTime() : null;
            voipDetailReportVO.setTalkMinutes(totalTime == null ? null : (totalTime > 60 ?  (Math.ceil((double) totalTime / 60)) : (totalTime == 0 ? 0d : 0.5d)));
            voipDetailReportVO.setCallResult(voipContactDTO.getCallResultId());
            voipDetailReportVO.setCallType(voipContactDTO.getCallTypeId());
            TalentNoteDTO talentNoteDTO = talentNotes.get(voipContactDTO.getPhoneCallId()) != null ?  talentNotes.get(voipContactDTO.getPhoneCallId()) : null;
            if(talentNoteDTO != null) {
                JSONObject summary = talentNoteDTO.getAdditionalInfo().get("summary") != null ? JSONUtil.parseObj(talentNoteDTO.getAdditionalInfo().get("summary")) : null;
                String note = talentNoteDTO.getNote();
                voipDetailReportVO.setAISummary(summary);
                voipDetailReportVO.setNote(note);
            }
            if (null != voipDetailReportVO.getJobId() && CollUtil.isNotEmpty(privateJobIds)) {
                voipDetailReportVO.setPrivateJob(privateJobIds.contains(voipDetailReportVO.getJobId()));
            }else{
                voipDetailReportVO.setPrivateJob(false);
            }
            return voipDetailReportVO;
        }).toList();
        switch (voipDetailReportDTO.getReportType()) {
            case CONNECT -> voipDetailReports = voipDetailReports.stream().filter(voipDetailReportVO -> voipDetailReportVO.getTalkMinutes() != null).toList();

            case QUALIFIED_CONNECT -> voipDetailReports = voipDetailReports.stream().filter(voipDetailReportVO -> voipDetailReportVO.getTalkMinutes() != null && voipDetailReportVO.getTalkMinutes() > 1).toList();
        }
        voipDetailReports = new ArrayList<>(voipDetailReports);
        voipDetailReports.forEach(voipDetailReportVO -> {
            if(voipDetailReportVO.getTalkMinutes() == null)
                voipDetailReportVO.setTalkMinutes(0d);
        });
        if(sort != null) voipDetailReports.sort(getVoipReportDetailComparatorFromSort(sort));
        return voipDetailReports;
    }



}
