package com.altomni.apn.report.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.enumeration.QuarterlyOnboardingType;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.ReportGpSearchDTO;
import com.altomni.apn.report.dto.ReportMonthlyRevenueGpSearchDTO;
import com.altomni.apn.report.dto.ReportQuarterOnboardAndOffboardSearchDTO;
import com.altomni.apn.report.repository.ReportGpRepository;
import com.altomni.apn.report.service.ReportGpService;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.job.JobService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.report.util.ReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service("reportGpService")
public class ReportGpServiceImpl implements ReportGpService {

    @Resource
    private ReportGpRepository reportGpRepository;

    @Resource
    protected InitiationService initiationService;

    @Resource
    CompanyService companyService;

    @Resource
    JobService jobService;

    /**
     * 查询gp report 列表数据
     *
     * @param reportGpSearchDTO
     */
    @Override
    public Page<ReportGpWithExcelVO> searchGp(ReportGpSearchDTO reportGpSearchDTO) {
        List<ReportGpWithExcelVO> dataList = reportGpRepository.searchReportGpWithExcelData(reportGpSearchDTO, false);
        Long count = reportGpRepository.countReportGp(reportGpSearchDTO);
        return new PageImpl<>(dataList, Pageable.unpaged(), count);
    }

    /**
     * 查询 report excel 数据（远多于列表数据）
     *
     * @param reportGpSearchDTO
     * @param response
     */
    @Override
    public void searchGpWithExcelData(ReportGpSearchDTO reportGpSearchDTO, HttpServletResponse response) {
        List<ReportGpWithExcelVO> dataList = reportGpRepository.searchReportGpWithExcelData(reportGpSearchDTO, true);
        ExcelUtil.downloadExcelCustom(response, ReportGpWithExcelVO.class, dataList, "", "GP_Report_" + reportGpSearchDTO.getFrom() + "_" + reportGpSearchDTO.getTo() + ".xlsx", true);
    }

    @Override
    public List<ReportMonthlyRevenueGpVO> searchMonthlyRevenueGpReport(ReportMonthlyRevenueGpSearchDTO reportMonthlyRevenueGpSearchDTO) throws Exception {
        log.info("[apn] searchMonthlyRevenueGpReport start ......");
        StopWatch stopWatch = new StopWatch("searchMonthlyRevenueGpReport");
        stopWatch.start("monthlyRevenueGp");

        SecurityContext context = SecurityContextHolder.getContext();

        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();


        List<String> monthList = getMonthsBetweenDates(reportMonthlyRevenueGpSearchDTO.getStartDate(), reportMonthlyRevenueGpSearchDTO.getEndDate());

        CompletableFuture<ConcurrentMap<String, String>> revenueFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countReportMonthlyRevenueGp(reportMonthlyRevenueGpSearchDTO,teamDataPermission);
        });

        CompletableFuture<ConcurrentMap<String, String>> cashInGPContractorFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countCashInGPContractorGp(reportMonthlyRevenueGpSearchDTO,teamDataPermission);
        });

        CompletableFuture<ConcurrentMap<String, String>> cashInGPFTEFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countCashInGPFTEGp(reportMonthlyRevenueGpSearchDTO,teamDataPermission);
        });

        CompletableFuture<ConcurrentMap<String, String>> forecastedGPFTEFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countForecastedGPFTE(reportMonthlyRevenueGpSearchDTO,teamDataPermission);
        });

        CompletableFuture<ConcurrentMap<String, String>> forecastedGPContractorFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countForecastedGPContractor(reportMonthlyRevenueGpSearchDTO,teamDataPermission);
        });

        ConcurrentMap<String, String> revenue = revenueFuture.join();
        ConcurrentMap<String, String> cashInGPContractor = cashInGPContractorFuture.join();
        ConcurrentMap<String, String> cashInGPFTE = cashInGPFTEFuture.join();
        ConcurrentMap<String, String> forecastedGPFTE = forecastedGPFTEFuture.join();
        ConcurrentMap<String, String> forecastedGPContractor = forecastedGPContractorFuture.join();

        List<ReportMonthlyRevenueGpVO> voList = new ArrayList<>();
        for (String month : monthList) {
            ReportMonthlyRevenueGpVO vo = new ReportMonthlyRevenueGpVO();
            vo.setMonth(month);
            if (revenue.containsKey(month)) {
                vo.setRevenue(completionNumber(revenue.get(month)));
            }
            if (cashInGPContractor.containsKey(month)) {
                vo.setCashInGPContractor(completionNumber(cashInGPContractor.get(month)));
            }
            if (cashInGPFTE.containsKey(month)) {
                vo.setCashInGPFTE(completionNumber(cashInGPFTE.get(month)));
            }
            if (forecastedGPFTE.containsKey(month)) {
                vo.setForecastedGPFTE(completionNumber(forecastedGPFTE.get(month)));
            }
            if (forecastedGPContractor.containsKey(month)) {
                vo.setForecastedGPContractor(completionNumber(forecastedGPContractor.get(month)));
            }
            voList.add(vo);
        }

        if (null != reportMonthlyRevenueGpSearchDTO.getSort()) {
            if (reportMonthlyRevenueGpSearchDTO.getSort().getDirection().equals("desc")) {
                voList = voList.stream().sorted(Comparator.comparing(ReportMonthlyRevenueGpVO::getMonth).reversed()).collect(Collectors.toList());
            } else {
                voList = voList.stream().sorted(Comparator.comparing(ReportMonthlyRevenueGpVO::getMonth)).collect(Collectors.toList());
            }
        } else {
            voList = voList.stream().sorted(Comparator.comparing(ReportMonthlyRevenueGpVO::getMonth)).collect(Collectors.toList());
        }
        stopWatch.stop();
        log.info("[apn] monthlyRevenueGp time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] monthlyRevenueGp end ......");
        return voList;
    }

    private String completionNumber(String numberStr){
        // 判断是否包含小数点
        if (!numberStr.contains(".")) {
            numberStr += ".00"; // 如果没有小数点，直接补全两位小数
        } else {
            // 分割整数部分和小数部分
            String[] parts = numberStr.split("\\.");
            String integerPart = parts[0];
            String decimalPart = parts[1];

            // 补全小数部分
            if (decimalPart.length() < 2) {
                decimalPart += "0".repeat(2 - decimalPart.length());
            }

            numberStr = integerPart + "." + decimalPart;
        }
        return numberStr;
    }

    @Override
    public Page<ReportMonthlyRevenueDetailVO> searchMonthlyRevenueDetail(ReportMonthlyRevenueGpSearchDTO reportMonthlyRevenueGpSearchDTO, Pageable pageable) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

        return reportGpRepository.selectMonthlyRevenueDetail(reportMonthlyRevenueGpSearchDTO, pageable,teamDataPermission);
    }

    @Override
    public void download(HttpServletResponse response, ReportMonthlyRevenueGpSearchDTO dto) {
        String[] startDate = dto.getStartDate().split("-");
        String[] endDate = dto.getEndDate().split("-");
        String fileName = "Monthly_Revenue_Detail_Report_From_" +
                startDate[1] + "-" + startDate[0]
                +"_To_"+
                endDate[1] + "-" + endDate[0]
                + ".xls";
        Pageable pageable = PageRequest.of(0, 100000);
        Page<ReportMonthlyRevenueDetailVO> page = searchMonthlyRevenueDetail(dto, pageable);
        List<ReportMonthlyRevenueDetailVO> detailVOList = page.getContent();
        List<ReportMonthlyRevenueDownloadVO> voList = new ArrayList<>();
        List<EnumDictDTO> countryList = companyService.findAllCountry().getBody();
        Map<String, String> countryNames = countryList.stream().collect(Collectors.toMap(x -> x.getId(), v -> v.getName()));

        List<EnumCurrency> currencyList = jobService.findAllEnumCurrency().getBody();
        Map<Integer, String> currencyNames = currencyList.stream().collect(Collectors.toMap(x -> x.getId(), v -> v.getLabel1()));
        for (ReportMonthlyRevenueDetailVO vo : detailVOList) {
            ReportMonthlyRevenueDownloadVO downloadVO = new ReportMonthlyRevenueDownloadVO();
            downloadVO.setJobType(vo.getJobType());
            downloadVO.setCompany(vo.getClientName());
            downloadVO.setTalentName(vo.getCandidateName());
            downloadVO.setOnboardDate(vo.getOnboardDate());
            downloadVO.setAm(vo.getAm());
            if (null != vo.getCoAmList() && !vo.getCoAmList().isEmpty()) {
                Map<String,List<UserCountryVO>> nameCountrys = vo.getCoAmList().stream().collect(Collectors.groupingBy(UserCountryVO::getUserName));
                List<String> coAm = new ArrayList<>();
                List<String> countryNameList = new ArrayList<>();
                nameCountrys.forEach((k,v)->{
                    v.forEach(x->{
                        String countryName = countryNames.get(x.getCountryId());
                        if(null != countryName){
                            countryNameList.add(countryName);
                        }
                    });
                    coAm.add(k + "(" + StringUtils.join(countryNameList, ",") + ")");
                });
               if(!coAm.isEmpty()){
                   downloadVO.setAm(downloadVO.getAm() + "," + StringUtils.join(coAm,","));
               }
            }
            downloadVO.setRecruiter(vo.getRecruiter());
            downloadVO.setSourcer(vo.getSourcer());
            downloadVO.setAc(vo.getAc());
            downloadVO.setDm(vo.getDm());
            downloadVO.setOwners(vo.getOwners());
            downloadVO.setSalesLeadOwner(vo.getSalesLeadOwner());
            downloadVO.setBdOwner(vo.getBdOwner());
            if (null != vo.getPlacementFeeNumber()) {
                downloadVO.setTotalBillAmount(vo.getPlacementFeeNumber().toString());
                if (StringUtils.isNotBlank(vo.getRateUnitType())) {
                    downloadVO.setCurrency(vo.getCurrencyName() + " - " + currencyNames.get(Integer.valueOf(vo.getCurrency())) + "/" + RateUnitType.fromDbValue(Integer.valueOf(vo.getRateUnitType())).getDescription());
                }
            }
            if (null != vo.getBillRateNumber()) {
                downloadVO.setBillBillRate(vo.getBillRateNumber().toString());
                if (StringUtils.isNotBlank(vo.getRateUnitType())) {
                    downloadVO.setBillRateCurrency(vo.getCurrencyName() + " - " + currencyNames.get(Integer.valueOf(vo.getCurrency())) + "/" + RateUnitType.fromDbValue(Integer.valueOf(vo.getRateUnitType())).getDescription());
                }
            }
            if (null != vo.getPayRateNumber()) {
                downloadVO.setFinalPayRate(vo.getPayRateNumber().toString());
                if (StringUtils.isNotBlank(vo.getRateUnitType())) {
                    downloadVO.setPayRateCurrency(vo.getCurrencyName() + " - " + currencyNames.get(Integer.valueOf(vo.getCurrency())) + "/" + RateUnitType.fromDbValue(Integer.valueOf(vo.getRateUnitType())).getDescription());

                }
            }
            voList.add(downloadVO);
        }
        ExcelUtil.downloadExcel(response, ReportMonthlyRevenueDownloadVO.class, voList, "", fileName, false);
    }

    @Override
    public List<ReportQuarterOnboardAndOffboardVO> searchQuarterlyOnboardingAndOffboardingReport(ReportQuarterOnboardAndOffboardSearchDTO dto) {
        log.info("[apn] searchMonthlyRevenueGpReport start ......");
        StopWatch stopWatch = new StopWatch("searchQuarterlyOnboardingAndOffboardingReport");
        stopWatch.start("QuarterlyOnboardingAndOffboardingReport");

        Map<String, List<String>> quartersMonths = mapQuartersToMonths(dto.getStartDate(), dto.getEndDate());

        SecurityContext context = SecurityContextHolder.getContext();

        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

        CompletableFuture<List<ReportQuarterlyOnboardAndOffboardCountVO>> onboardCountFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countReportQuarterlyOnboardingGp(dto,teamDataPermission);
        });

        CompletableFuture<List<ReportQuarterlyOnboardAndOffboardCountVO>> onboardRenewalsCountFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countReportQuarterlyOnboardingRenewalsGp(dto,teamDataPermission);
        });

        CompletableFuture<List<ReportQuarterlyOnboardAndOffboardCountVO>> offboardCountFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countReportQuarterlyOffboardingGp(dto,teamDataPermission);
        });

        CompletableFuture<List<ReportQuarterlyOnboardAndOffboardCountVO>> forecastedGPCountFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countQuarterlyForecastedGPContractor(dto,teamDataPermission);
        });

        CompletableFuture<List<ReportQuarterlyOnboardAndOffboardCountVO>> fteGPCountFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return reportGpRepository.countQuarterlyForecastedGPFTE(dto,teamDataPermission);
        });
        stopWatch.stop();
        stopWatch.start("sql search info");
        List<ReportQuarterlyOnboardAndOffboardCountVO> onboardCount = onboardCountFuture.join();
        List<ReportQuarterlyOnboardAndOffboardCountVO> onboardRenewalsCount = onboardRenewalsCountFuture.join();
        List<ReportQuarterlyOnboardAndOffboardCountVO> offboardCount = offboardCountFuture.join();
        List<ReportQuarterlyOnboardAndOffboardCountVO> forecastedGPCount = forecastedGPCountFuture.join();
        List<ReportQuarterlyOnboardAndOffboardCountVO> fteGPCount = fteGPCountFuture.join();
        stopWatch.stop();

        stopWatch.start("Assembling data");
        List<ReportQuarterOnboardAndOffboardVO> voList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : quartersMonths.entrySet()) {
            String quarter = entry.getKey();
            List<String> months = entry.getValue();
            ReportQuarterOnboardAndOffboardVO vo = new ReportQuarterOnboardAndOffboardVO();
            vo.setQuarter(quarter);

            BigDecimal numberOfOnboardGp = BigDecimal.ZERO;
            BigDecimal numberOfOffboardGp = BigDecimal.ZERO;
            BigDecimal forecastedGp = BigDecimal.ZERO;
            BigDecimal fteGP = BigDecimal.ZERO;
            AtomicInteger numberOfOnboardCount = new AtomicInteger(0);
            AtomicInteger numberOfOffboardCount = new AtomicInteger(0);
            for (String month : months) {

                Set<String> startIds = new HashSet<>();
                Set<String> renewalStartIds = new HashSet<>();
                Set<String> offboardStartIds = new HashSet<>();
                Set<String> forecasteStartIds = new HashSet<>();

                fteGP = fteGP.add(fteGPCount.stream()
                        .filter(v -> v.getYearMonthStr().equals(month))
                        .filter(x -> StringUtils.isNotBlank(x.getTotal()))
                        .map(x -> new BigDecimal(x.getTotal())).reduce(BigDecimal.ZERO, BigDecimal::add));

                for (ReportQuarterlyOnboardAndOffboardCountVO v : forecastedGPCount) {
                    if (v.getYearMonthStr().equals(month) && !forecasteStartIds.contains(v.getStartId())) {
                        if(StringUtils.isNotBlank(v.getTotal())){
                            forecastedGp = forecastedGp.add(new BigDecimal(v.getTotal()));
                        }
                        forecasteStartIds.add(v.getStartId());
                    }
                }


                for (ReportQuarterlyOnboardAndOffboardCountVO v : onboardCount) {
                    if (v.getYearMonthStr().equals(month) && !startIds.contains(v.getStartId())) {
                        numberOfOnboardCount.incrementAndGet();
                        if(StringUtils.isNotBlank(v.getTotal())){
                            numberOfOnboardGp = numberOfOnboardGp.add(new BigDecimal(v.getTotal()));
                        }
                        startIds.add(v.getStartId());
                    }
                }


                for (ReportQuarterlyOnboardAndOffboardCountVO v : onboardRenewalsCount) {
                    if (v.getYearMonthStr().equals(month) && !renewalStartIds.contains(v.getStartId())) {
                        numberOfOnboardCount.incrementAndGet();
                        if(StringUtils.isNotBlank(v.getTotal())){
                            numberOfOnboardGp = numberOfOnboardGp.add(new BigDecimal(v.getTotal()));
                        }
                        renewalStartIds.add(v.getStartId());
                    }
                }


                for (ReportQuarterlyOnboardAndOffboardCountVO v : offboardCount) {
                    if (v.getYearMonthStr().equals(month) && !offboardStartIds.contains(v.getStartId())) {
                        numberOfOffboardCount.incrementAndGet();
                        if(StringUtils.isNotBlank(v.getTotal())){
                            numberOfOffboardGp = numberOfOffboardGp.add(new BigDecimal(v.getTotal()));
                        }
                        offboardStartIds.add(v.getStartId());
                    }
                }
            }
            vo.setNumberOfOnboardGp(numberOfOnboardGp.toString());
            vo.setNumberOfOnboard(numberOfOnboardCount.get() + "");
            vo.setNumberOfOffboard(numberOfOffboardCount.get() + "");
            vo.setNumberOfOffboardGp(numberOfOffboardGp.toString());
            vo.setForecastedGp(forecastedGp + "");
            vo.setFteGp(fteGP + "");
            voList.add(vo);
        }

        stopWatch.stop();
        stopWatch.start("convertTask");

        if (null != dto.getSort()) {
            if (dto.getSort().getDirection().equals("desc")) {
                voList = voList.stream().sorted(Comparator.comparing(ReportQuarterOnboardAndOffboardVO::getQuarter).reversed()).collect(Collectors.toList());
            } else {
                voList = voList.stream().sorted(Comparator.comparing(ReportQuarterOnboardAndOffboardVO::getQuarter)).collect(Collectors.toList());
            }
        }

        stopWatch.stop();
        log.info("[apn] QuarterlyOnboardingAndOffboardingReport time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] QuarterlyOnboardingAndOffboardingReport end ......");
        return voList;
    }

    @Override
    public Page<ReportQuarterlyOnboardingDetailVO> searchQuarterlyOnboardingDetail(ReportQuarterOnboardAndOffboardSearchDTO searchDto, Pageable pageable) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

        return reportGpRepository.selectQuarterlyNewHireAndRenewalDetail(searchDto, pageable,teamDataPermission);
    }

    @Override
    public Page<ReportQuarterlyOffboardingDetailVO> searchQuarterlyOffboardingDetail(ReportQuarterOnboardAndOffboardSearchDTO searchDto, Pageable pageable) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

        return reportGpRepository.selectQuarterlyOffonboardingDetail(searchDto, pageable,teamDataPermission);
    }

    @Override
    public void quarterlyOnboardAndOffboardingDetailDownload(HttpServletResponse response, ReportQuarterOnboardAndOffboardSearchDTO dto) {

        try {
            Pageable pageable = PageRequest.of(0, 100000);
            dto.setType(QuarterlyOnboardingType.NEWHIRES);
            Page<ReportQuarterlyOnboardingDetailVO> newHiresPage = searchQuarterlyOnboardingDetail(dto, pageable);
            List<ReportQuarterlyOnboardingDetailVO> newHiresVOList = newHiresPage.getContent();

            dto.setType(QuarterlyOnboardingType.RENEWALS);
            Page<ReportQuarterlyOnboardingDetailVO> renewalsPage = searchQuarterlyOnboardingDetail(dto, pageable);
            List<ReportQuarterlyOnboardingDetailVO> renewalsVOList = renewalsPage.getContent();

            BigDecimal gpSum = BigDecimal.ZERO;
            BigDecimal totalSum = BigDecimal.ZERO;
            BigDecimal revenueSum = BigDecimal.ZERO;
            Pair<Integer, Integer> firstMergeCell = null;
            Integer firstEndIndex = 3;
            List<ReportQuarterlyOnboardingDetailDownloadVO> downloadVOS = new ArrayList<>();
            if(newHiresVOList.get(0).getId() !=null){
                addDownloadDate(downloadVOS,newHiresVOList,"Contractor New Hires");
                firstEndIndex = newHiresVOList.size() - 1 + 2;
                firstMergeCell = new Pair<>(3, firstEndIndex);
                ReportQuarterlyOnboardingDetailVO detailVO = newHiresVOList.get(newHiresVOList.size()-1);
                ReportQuarterlyOnboardingDetailDownloadVO newHiresTotal = new ReportQuarterlyOnboardingDetailDownloadVO();
                newHiresTotal.setEmployeeType("New Hires Total");
                newHiresTotal.setNumber(newHiresVOList.size() - 1);
                newHiresTotal.setHourlyGp("$ " + detailVO.getTotal().getHourlyGpSum());
                newHiresTotal.setTotalGp("$ " + detailVO.getTotal().getTotalGpSum());
                newHiresTotal.setTotalRevenue("$ " + detailVO.getTotal().getTotalRevenueSum());
                gpSum = addSum(detailVO.getTotal().getHourlyGpSum(),gpSum);
                totalSum = addSum(detailVO.getTotal().getTotalGpSum(),totalSum);
                revenueSum = addSum(detailVO.getTotal().getTotalRevenueSum(),revenueSum);
                downloadVOS.add(newHiresTotal);
            }

            Pair<Integer, Integer> secondMergeCell = null;
            if (renewalsVOList.get(0).getId() != null) {
                addDownloadDate(downloadVOS, renewalsVOList, "Contractor Renewals");
                Integer secondEndIndex = firstEndIndex == 3 ? firstEndIndex + renewalsVOList.size() - 2 : firstEndIndex + 1 + renewalsVOList.size() - 1;
                secondMergeCell = new Pair<>(firstEndIndex == 3 ? 3 : firstEndIndex + 2, secondEndIndex);
                ReportQuarterlyOnboardingDetailVO renewalDetailVO = renewalsVOList.get(renewalsVOList.size() - 1);
                ReportQuarterlyOnboardingDetailDownloadVO renewalTotal = new ReportQuarterlyOnboardingDetailDownloadVO();
                renewalTotal.setEmployeeType("Renewals Total");
                renewalTotal.setNumber(renewalsVOList.size() - 1);
                renewalTotal.setHourlyGp("$ " + renewalDetailVO.getTotal().getHourlyGpSum());
                renewalTotal.setTotalGp("$ " + renewalDetailVO.getTotal().getTotalGpSum());
                renewalTotal.setTotalRevenue("$ " + renewalDetailVO.getTotal().getTotalRevenueSum());
                downloadVOS.add(renewalTotal);

                gpSum = addSum(renewalDetailVO.getTotal().getHourlyGpSum(), gpSum);
                totalSum = addSum(renewalDetailVO.getTotal().getTotalGpSum(), totalSum);
                revenueSum = addSum(renewalDetailVO.getTotal().getTotalRevenueSum(), revenueSum);
            }

            ReportQuarterlyOnboardingDetailDownloadVO grandTotal = new ReportQuarterlyOnboardingDetailDownloadVO();
            grandTotal.setEmployeeType("Grand Total");
            grandTotal.setNumber((renewalsVOList.size() - 1) + (newHiresVOList.size() - 1));
            grandTotal.setHourlyGp("$ " + gpSum);
            grandTotal.setTotalGp("$ " + totalSum);
            grandTotal.setTotalRevenue("$ " + revenueSum);
            downloadVOS.add(grandTotal);


            String[] dates = dto.getStartDate().split("-");
            String name = "Q" + dates[1] + " " + dates[0] + " Newhires and Renewals--Contractor";
            String fileName = "Q" + dates[1] + "_" + dates[0] + "_Newhires_and_Renewals--Contractor.xls";
            com.altomni.apn.common.utils.ExcelUtil.getResourceMergedRegionCowAndCol(response,
                    fileName
                    , ReportUtil.REPORT_QUARTERLY_ONBOARDING_DOCUMENTS_FIELDS, new ArrayList<>(downloadVOS)
                    , name, firstMergeCell, secondMergeCell);
        } catch (Exception e) {
            log.error("[quarterlyOffboardingDetail download]: error:{}", e);
        }

    }

    private BigDecimal addSum(String gp,BigDecimal sum){
        if(StringUtils.isNotBlank(gp)){
            sum = sum.add(new BigDecimal(gp));
        }
        return sum;
    }

    private void addDownloadDate(List<ReportQuarterlyOnboardingDetailDownloadVO> downloadVOS,
                         List<ReportQuarterlyOnboardingDetailVO> voList,String title){
        List<EnumCurrency> currencyList = jobService.findAllEnumCurrency().getBody();
        Map<Integer, String> countryNames = currencyList.stream().collect(Collectors.toMap(x -> x.getId(), v -> v.getLabel1()));
        for (int i = 0; i < voList.size() - 1; i++) {
            ReportQuarterlyOnboardingDetailVO detailVO = voList.get(i);
            ReportQuarterlyOnboardingDetailDownloadVO vo = new ReportQuarterlyOnboardingDetailDownloadVO();
            if (i == 0) {
                vo.setEmployeeType(title);
            }

            vo.setNumber(i + 1);
            vo.setRecruiter(detailVO.getRecruiter());
            vo.setCandidateName(detailVO.getCandidateName());
            vo.setClientAccount(detailVO.getClientAccount());
            vo.setStartDay(detailVO.getStartDay());
            vo.setWeekDay("week " + detailVO.getWeekDay());
            vo.setHourlyGp(detailVO.getSymbol() + " " + detailVO.getHourlyGp());
            vo.setTotalGp(detailVO.getSymbol() + " " + detailVO.getTotalGp());
            vo.setTotalRevenue(detailVO.getSymbol() + " " + detailVO.getTotalRevenue());

            if(StringUtils.isNotBlank(detailVO.getBillRate())){
                vo.setBillRateCurrency(detailVO.getSymbol());
                vo.setBillRate(detailVO.getBillRate());
            } else {
                vo.setBillRateCurrency("--");
                vo.setBillRate("--");
            }

            if(StringUtils.isNotBlank(detailVO.getPayRate())){
                vo.setPayRateCurrency(detailVO.getSymbol());
                vo.setPayRate(detailVO.getPayRate());
            } else {
                vo.setPayRateCurrency("--");
                vo.setPayRate("--");
            }

            if(StringUtils.isNotBlank(detailVO.getOriginalBillRate())){
                vo.setOriginalBillRateCurrency(countryNames.get(Integer.valueOf(detailVO.getOriginalCurrency())));
                vo.setOriginalBillRate(detailVO.getOriginalBillRate());
            } else {
                vo.setOriginalBillRateCurrency("--");
                vo.setOriginalBillRate("--");
            }

            if(StringUtils.isNotBlank(detailVO.getOriginalPayRate())){
                vo.setOriginalPayRateCurrency(countryNames.get(Integer.valueOf(detailVO.getOriginalCurrency())));
                vo.setOriginalPayRate(detailVO.getOriginalPayRate());
            } else {
                vo.setOriginalPayRateCurrency("--");
                vo.setOriginalPayRate("--");
            }

            vo.setNotes(detailVO.getNotes());
            downloadVOS.add(vo);
        }
    }

    @Override
    public void quarterlyOffboardingDetailDownload(HttpServletResponse response, ReportQuarterOnboardAndOffboardSearchDTO dto) {
        try {
            Pageable pageable = PageRequest.of(0, 100000);
            Page<ReportQuarterlyOffboardingDetailVO> page = searchQuarterlyOffboardingDetail(dto, pageable);
            List<ReportQuarterlyOffboardingDetailVO> detailVOList = page.getContent();
            List<ReportQuarterlyOffboardingDetailDownloadVO> downloadVOS = new ArrayList<>();
            for (int i = 0; i < detailVOList.size() - 1; i++) {
                ReportQuarterlyOffboardingDetailVO detailVO = detailVOList.get(i);
                ReportQuarterlyOffboardingDetailDownloadVO vo = new ReportQuarterlyOffboardingDetailDownloadVO();
                if (i == 0) {
                    vo.setEmployeeType("Contractor Terminated");
                }
                vo.setNumber(i + 1);
                vo.setRecruiter(detailVO.getRecruiter());
                vo.setCandidateName(detailVO.getCandidateName());
                vo.setClientAccount(detailVO.getClientAccount());
                vo.setEndDay(detailVO.getEndDay());
                vo.setWeekDay("week " + detailVO.getWeekDay());
                vo.setHourlyGp(detailVO.getSymbol() + " " + (StringUtils.isBlank(detailVO.getHourlyGp()) ? 0 : detailVO.getHourlyGp()));
                vo.setTotalGp(detailVO.getSymbol() + " " + (StringUtils.isBlank(detailVO.getTotalGp()) ? 0 : detailVO.getTotalGp()));
                vo.setTotalRevenue(detailVO.getSymbol() + " " + (StringUtils.isBlank(detailVO.getTotalRevenue()) ? 0 : detailVO.getTotalRevenue()));
                downloadVOS.add(vo);
            }
            ReportQuarterlyOffboardingDetailVO detailVO = detailVOList.get(detailVOList.size() - 1);
            String[] otherDate = new String[10];
            otherDate[0] = "Terminated Total";
            otherDate[1] = detailVOList.size() - 1 + "";
            otherDate[2] = "";
            otherDate[3] = "";
            otherDate[4] = "";
            otherDate[5] = "";
            otherDate[6] = "";
            otherDate[7] = "$ " + detailVO.getTotal().getHourlyGpSum();
            otherDate[8] = "$ " + detailVO.getTotal().getTotalGpSum();
            otherDate[9] = "$ " + detailVO.getTotal().getTotalRevenueSum();


            String[] dates = dto.getStartDate().split("-");
            String name = "Q" + dates[1] + " " + dates[0] + " Terminated--Contractor";
            String fileName = "Q" + dates[1] + "_" + dates[0] + "_Terminated--Contractor.xls";
            com.altomni.apn.common.utils.ExcelUtil.getResource(response,
                    fileName
                    , ReportUtil.REPORT_QUARTERLY_OFFBOARDING_DOCUMENTS_HEADERS
                    , ReportUtil.REPORT_QUARTERLY_OFFBOARDING_DOCUMENTS_FIELDS, new ArrayList<>(downloadVOS)
                    , otherDate, null, null, name);
        } catch (Exception e) {
            log.error("[quarterlyOffboardingDetail download]: error:{}", e);
        }
    }

    public List<String> getMonthsBetweenDates(String startDate, String endDate) {
        List<String> months = new ArrayList<>();

        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析开始日期和结束日期
        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        // 如果开始日期晚于结束日期，直接返回空列表
        if (start.isAfter(end)) {
            return months;
        }

        // 循环计算月份
        while (!start.isAfter(end)) {
            // 格式化当前月份
            String month = start.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            months.add(month);

            // 增加一个月
            start = start.plusMonths(1);
        }

        return months;
    }

    public Map<String, List<String>> mapQuartersToMonths(String startDate, String endDate) {
        String[] startParts = startDate.split("-");
        String[] endParts = endDate.split("-");

        if (startParts.length != 2 || endParts.length != 2) {
            throw new IllegalArgumentException("Invalid date format. Expected format: yyyy-Q");
        }

        int startYear = Integer.parseInt(startParts[0]);
        int startQuarter = Integer.parseInt(startParts[1]);
        int endYear = Integer.parseInt(endParts[0]);
        int endQuarter = Integer.parseInt(endParts[1]);

        Map<String, List<String>> quarterToMonthsMap = new HashMap<>();

        for (int year = startYear; year <= endYear; year++) {
            int startQ = (year == startYear) ? startQuarter : 1;
            int endQ = (year == endYear) ? endQuarter : 4;

            for (int quarter = startQ; quarter <= endQ; quarter++) {
                String key = year + "-" + quarter;
                List<String> months = new ArrayList<>();
                for (int month = (quarter - 1) * 3 + 1; month <= quarter * 3; month++) {
                    months.add(String.format("%d-%02d", year, month));
                }
                quarterToMonthsMap.put(key, months);
            }
        }
        return quarterToMonthsMap;
    }
}
