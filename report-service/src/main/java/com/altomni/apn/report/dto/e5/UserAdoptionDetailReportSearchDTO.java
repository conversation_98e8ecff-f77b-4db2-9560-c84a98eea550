package com.altomni.apn.report.dto.e5;

import com.altomni.apn.report.domain.enumeration.E5ReportViewType;
import com.altomni.apn.report.domain.enumeration.UserAdoptionReportSearchType;
import com.altomni.apn.report.dto.ApplicationDetailSearchDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionDetailReportSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startTime;

    private LocalDate endTime;

    private Long userId;

    private UserAdoptionReportSearchType type;

    private ApplicationDetailSearchDto search;

    //for download use only! Convert all time to user-set timezone
    private String timeZone;

    private E5ReportViewType viewType;

    private Long teamId;

}
