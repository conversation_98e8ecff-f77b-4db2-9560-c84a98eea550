package com.altomni.apn.report.dto.e5;

import com.altomni.apn.report.domain.enumeration.E5ReportViewType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startTime;

    private LocalDate endTime;

    private List<Long> teamIdList;

    private List<Long> userIdList;

    private Boolean lastWeekActivityBelowAverage;

    private Boolean ignoreThreshold;

    private E5ReportViewType viewType;

}
