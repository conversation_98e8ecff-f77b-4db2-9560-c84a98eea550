package com.altomni.apn.report.service.e5;

import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.altomni.apn.report.domain.vo.e5.TeamAdoptionReportVO;
import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportVO;
import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportWithThresholdVO;
import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportDTO;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface UserAdoptionReportService {

    Integer getActiveDurationThreshold();

    Map<Long, Long> getLastWeekBelowAverageActiveDurationUserByTeamId(Long tenantId, List<Long> teamId);

    UserAdoptionReportWithThresholdVO getUserAdoptionReport(UserAdoptionReportDTO userAdoptionReportDTO, Pageable pageable) throws ExecutionException, InterruptedException;

    List<TeamAdoptionReportVO> getUserAdoptionReportByTeamView(List<UserAdoptionReportVO> userAdoptionReportVOList, Pageable pageable);

    UserAdoptionReportWithThresholdVO getUserAdoptionReportForDashboard(String view, UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException;

    void exportE5UserAdoptionReportToExcel(UserAdoptionReportDTO userAdoptionReportDTO, HttpServletResponse response) throws ExecutionException, InterruptedException;

    UserActiveDurationStatistic getLastWeekActiveDurationUserInfo(GetLastWeekActiveDurationUserInfoDTO dto);
}
