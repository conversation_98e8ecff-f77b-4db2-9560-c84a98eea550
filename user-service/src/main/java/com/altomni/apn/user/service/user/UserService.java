package com.altomni.apn.user.service.user;

import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.sso.SsoUserActive;
import com.altomni.apn.common.dto.sso.SsoUserBinding;
import com.altomni.apn.common.dto.sso.SsoUserInfo;
import com.altomni.apn.common.dto.user.*;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberWithDeliveryLabelDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.service.dto.permission.RelateJobFolderTeamTreeDTO;
import com.altomni.apn.user.service.dto.user.*;
import com.altomni.apn.user.service.query.UserSearch;
import com.altomni.apn.user.web.rest.vm.ManagedUserVM;
import com.altomni.apn.user.web.rest.vm.RefreshTokenVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamDeliverySearchVM;
import com.altomni.apn.user.web.rest.vm.permission.RelateJobFolderUserInfo;
import com.altomni.apn.user.web.rest.vm.user.UserPushIntervalDTO;
import com.altomni.apn.user.web.rest.vm.user.UserVM;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * Service class for managing users.
 */
public interface UserService{

    Optional<User> getUserWithAuthoritiesByLogin(String login);

    Optional<User> completePasswordReset(String newPassword, String key);

    Optional<User> requestPasswordReset(String mail);

    User registerAccount(ManagedUserVM managedUserVM);

    User createUser(UserDTO userDTO);


    void updateUser(String firstName, String lastName, String email, String langKey, String imageUrl);

    UserDTO updateUser(Long userId, UserDTO userDTO);

    void changePassword(String password);

    Page<UserVM> getAllManagedUsers(UserSearch userSearch);

    Page<UserDTO> getAllActivatedUsers(Pageable pageable);

    Optional<User> findOneByLogin(String login);

    User getCurrentUser();

    List<Role> getAuthorities();

    String getUserFullName(Long userId);

    User findOne(Long userId);

    User createLimitUser(ManagedUserVM managedUserVM);

    List<UserBriefDTO> getAllBriefUsers(Long tenantId);

    List<String> getAllValidUserEmails();

    List<PermissionTeamUserDTO> getAllPermissionUsers(Long tenantId);

    List<UserWithRolesAndTeamsDTO> getAllUsersWithRolesAndTeams();

    List<UserBriefDTO> getBriefUsersByIds(List<Long> ids);

    List<UserBriefDTO> getAllBriefUsersByIds(List<Long> ids);

    List<UserBriefDTO> getBriefUsersByUids(List<String> uids);

    List<User> getExistUsers(List<Long> userIds);

    List<User> getAllUsersByIds(List<Long> userIds);

    Map<String, UserUidNameDTO> getUidNameMapByUids(List<String> uids);

    Map<Long, UserUidNameDTO> getUidNameMapByIdsOrTenant(Set<Long> ids);

    Map<String, UserUidNameDTO> getUidNameMapByUidsOrTenant(List<String> uids);

    List<UserBriefDTO> searchBriefUsers(String name, Long tenantId);

    Set<String> getUserCountriesByTenantId(Long tenantId);

    List<UserBriefDTO> getAllBriefUsersByUsername(String username);

    void createTenantAdminUser(TenantAdminUserDto userDTO);

    Page<User> findByUserName(BooleanExpression booleanExpression, Pageable pageable);

    List<UserBriefDTO> toUserBriefDTOList(List<User> users);

//    void updateTenantAndActiveUserCredit();

//    void updateTenantAndActiveUserCreditDebugOnly();

    User findFirstUserByTenantIdAndAuthorityName(Long tenantId, String authorityName);

    List<PermissionTeamMemberDTO> findUsersByPermissionTeamId(Long permissionTeamId);

    List<PermissionTeamMemberDTO> findUsersByPermissionTeamIdIn(Set<Long> permissionTeamIds);

    List<PermissionTeamUserDTO> findActiveUsersByPermissionTeamIdIn(Set<Long> permissionTeamIds);

    List<UserBriefDTO> findUsersNotInTeam(Long teamId);

    Set<Long> findAllUserIdByTeamId(Long teamId);

    List<UserBriefDTO> findAllUserIdByIdsOrTenant(Set<Long> ids);

    List<UserBriefDTO> findAllActivatedBriefByTeamId(Long teamId);

    List<PermissionTeamMemberDTO> findPlainUsersByPermissionTeamId(Long teamId);

    List<PermissionTeamMemberDTO> findPlainUsersByPermissionTeamIdIn(Set<Long> teamIds);

    List<PermissionTeamUserDTO> findTeamUsersByPermissionTeamIdIn(Set<Long> teamIds);

    Set<Long> findTeamUserIdsByPermissionTeamIdIn(Set<Long> teamIds);

    Set<Long> findActiveTeamUserIdsByPermissionTeamIdIn(Set<Long> teamIds);

    UserDTO getAccount() throws ExecutionException, InterruptedException;

    LoginUserDTO login(LoginVM loginVM);

    List<User> getTenantAdminUsers();

    User createTenantAdminUser(UserDTO userDTO, String roleName, Long tenantId);

    CredentialDTO refreshToken(RefreshTokenVM refreshTokenVM);

    Page<UserPageVO> searchUserList(Long tenantId, Pageable pageable) throws ExecutionException, InterruptedException;

    void updateUserStatus(Long userId, UserStatusDTO userStatusDTO);

    void updateTimezoneByUserId(Long userId, UserTimezoneDTO userTimezoneDTO);

    UserTimeZoneVO getTimezoneByUserId(Long userId);

    void updateSyncLark(Long userId, Integer syncLark);

    Integer getSyncLark(Long userId);

    List<UserTimeZoneVO> getTimezoneListByUserIdList(List<Long> ids);

    List<RelateJobFolderUserInfo> getRelateJobFolderUsers(Long jobId);

    List<RelateJobFolderTeamTreeDTO> getRelateJobFolderTreeUsers(Long jobId);

    List<RelateJobFolderTeamTreeDTO> getTeamTreeUsers();

    void updateBYSsoUserInfo(SsoUserInfo ssoUserInfo);

    void updateUserActiveBySso(SsoUserActive userActive);

    User onSsoUserBinding(SsoUserBinding ssoUserBinding);
    Set<Long> findUserIdsInTheSameTeams(List<Long> teamIds);

    Object getAllBriefUsersWithPermissionByType(Module module);

    List<Long> getTeamsByUserId(Long userId);

    List<PermissionTeamMemberWithDeliveryLabelDTO> findUsersWithDeliveryLabelByPermissionTeamId(PermissionTeamDeliverySearchVM searchVM);

    void updatePushTime();

    Long getUserPushInterval(UserPushIntervalDTO dto);

    List<Long> getAllInactiveByidIn(Set<Long> ids);

    List<TeamInfoVO> getTeamInfoList(List<Long> userIds);
}