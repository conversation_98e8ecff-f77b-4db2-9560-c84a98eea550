package com.altomni.apn.user.web.rest.user;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.common.aop.datasync.DataSyncAspect;
import com.altomni.apn.common.aop.datasync.annotation.DataSyncAnnotation;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.email.UserResetDTO;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.dto.user.TenantAdminUserDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.config.env.ApplicationProperties;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.ApnParam;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.altomni.apn.user.repository.user.ApnParamRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.cache.CacheUser;
import com.altomni.apn.user.service.dto.permission.RelateJobFolderTeamTreeDTO;
import com.altomni.apn.user.service.dto.user.*;
import com.altomni.apn.user.service.mail.MailService;
import com.altomni.apn.user.service.query.UserSearch;
import com.altomni.apn.user.service.talent.TalentService;
import com.altomni.apn.user.service.user.CommonService;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.service.user.UserSkillTagService;
import com.altomni.apn.user.web.rest.vm.ManagedUserVM;
import com.altomni.apn.user.web.rest.vm.SkillTagDTO;
import com.altomni.apn.user.web.rest.vm.permission.RelateJobFolderUserInfo;
import com.altomni.apn.user.web.rest.vm.user.UserPushIntervalDTO;
import com.altomni.apn.user.web.rest.vm.user.UserVM;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * REST controller for managing users.
 * <p>
 * This class accesses the {@link User} entity, and needs to fetch its collection of authorities.
 * <p>
 * For a normal use-case, it would be better to have an eager relationship between User and Authority,
 * and send everything to the client side: there would be no View Model and DTO, a lot less code, and an outer-join
 * which would be good for performance.
 * <p>
 * We use a View Model and a DTO for 3 reasons:
 * <ul>
 * <li>We want to keep a lazy association between the user and the authorities, because people will
 * quite often do relationships with the user, and we don't want them to get the authorities all
 * the time for nothing (for performance reasons). This is the #1 goal: we should not impact our users'
 * application because of this use-case.</li>
 * <li> Not having an outer join causes n+1 requests to the database. This is not a real issue as
 * we have by default a second-level cache. This means on the first HTTP call we do the n+1 requests,
 * but then all authorities come from the cache, so in fact it's much better than doing an outer join
 * (which will get lots of data from the database, for each HTTP call).</li>
 * <li> As this manages users, for security reasons, we'd rather have a DTO layer.</li>
 * </ul>
 * <p>
 * Another option would be to have a specific JPA entity graph to handle this case.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class UserResource {

    private static final List<String> ALLOWED_ORDERED_PROPERTIES = List.of("id", "username", "firstName", "lastName", "email", "activated", "createdBy", "createdDate", "lastModifiedBy", "lastModifiedDate");

    @Resource
    private UserService userService;

    @Resource
    private CommonService commonService;

    @Resource
    private UserRepository userRepository;

//    @Resource
//    private TalentService talentService;

    @Resource
    private MailService mailService;

    @Resource
    private ApnParamRepository apnParamRepository;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private CacheUser cacheUser;

    @Resource
    private ApplicationProperties applicationProperties;



    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource
    private TalentService talentService;

    @Resource
    private CommonRedisService redisService;

    private boolean onlyContainsAllowedProperties(Pageable pageable) {
        return pageable.getSort().stream().map(Sort.Order::getProperty).allMatch(ALLOWED_ORDERED_PROPERTIES::contains);
    }

    /**
     * get all brief users by uids
     * @param uids the values of the column uid in user table
     * @return the list of UserBriefDTO
     */
    @PostMapping("/users/get-all-by-uid-in")
    public ResponseEntity<List<UserBriefDTO>> getAllByUidIn(@RequestBody List<String> uids) {
        log.info("({},{}) REST request to get all brief users by uids: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), uids);
        return ResponseEntity.ok(userService.getBriefUsersByUids(uids));
    }

    @PostMapping("/users/get-all-inactive-user")
    public ResponseEntity<List<Long>> getAllInactiveByidIn(@RequestBody Set<Long> ids) {
        log.info("({},{}) REST request to get all inactive users by ids: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(userService.getAllInactiveByidIn(ids));
    }

    /***
     * activity change function: to get user full name by the id;
     * @param uids
     * @return
     */
    @PostMapping("/users/get-user-names-by-uid-in")
    public ResponseEntity<Map<String, UserUidNameDTO>> getAllUserNameByUidIn(@RequestBody List<String> uids) {
        log.info("({},{}) REST request to get all name users by uids: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), uids);
        return ResponseEntity.ok(userService.getUidNameMapByUids(uids));
    }

    /***
     * activity change function: to get user full name by the ids or tenant id;
     * @param ids
     * @return
     */
    @PostMapping("/users/get-user-names-by-id-in-or-tenant")
    public ResponseEntity<Map<Long, UserUidNameDTO>> getAllUserNameByIdInOrTenant(@RequestBody Set<Long> ids) {
        log.info("({},{}) REST request to get all name users by ids or tenant id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(userService.getUidNameMapByIdsOrTenant(ids));
    }

    /***
     * activity change function: to get user full name by the uid or tenant id;
     * @param uids
     * @return
     */
    @PostMapping("/users/get-user-names-by-uid-in-or-tenant")
    public ResponseEntity<Map<String, UserUidNameDTO>> getAllUserNameByUidInOrTenant(@RequestBody List<String> uids) {
        log.info("({},{}) REST request to get all name users by uids or tenant id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), uids);
        return ResponseEntity.ok(userService.getUidNameMapByUidsOrTenant(uids));
    }

    @GetMapping("/users/find-user/tenant-and-authority")
    public ResponseEntity<User> findFirstUserByTenantIdAndAuthorityName(@RequestParam("tenantId") Long tenantId, @RequestParam("authorityName") String authorityName){
        log.info("({},{}) REST request to get first user by tenant id and authority name: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), tenantId, authorityName);
        return ResponseEntity.ok(userService.findFirstUserByTenantIdAndAuthorityName(tenantId, authorityName));
    }

    /**
     * POST  /users  : Creates a new user.
     * <p>
     * Creates a new user if the login and email are not already used, and sends an
     * email with an activation link.
     * The user needs to be activated on creation.
     *
     * @param managedUserVM the user to create
     * @return the ResponseEntity with status 201 (Created) and with body the new user, or with status 400 (Bad Request) if the login or email is already in use
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @PostMapping("/users")
    @NoRepeatSubmit
    public ResponseEntity<User> createUser(@Valid @RequestBody ManagedUserVM managedUserVM) throws URISyntaxException {
        log.info("[APN: User @{}] REST request to save User : {}", SecurityUtils.getUserId(), managedUserVM);

        if (managedUserVM.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATETEAM_TEAMIDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            // Lowercase the user login before comparing with database
        } else if (userService.findOneByLogin(managedUserVM.getUsername()).isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_USERNAMEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        } else if (userRepository.findOneByEmail(managedUserVM.getEmail()).isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_EMAILEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        } else {
            /*if (SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN)) {
                managedUserVM.setTenantId(SecurityUtils.getTenantId());
            }*/
            managedUserVM.setTenantId(SecurityUtils.getTenantId());
            User newUser = userService.createUser(managedUserVM);
            UserResetDTO userResetDTO = new UserResetDTO();
            BeanUtils.copyProperties(newUser, userResetDTO);
            userResetDTO.setBaseUrl(applicationProperties.getBaseUrl());
            mailService.sendResetPasswordInitEmail(userResetDTO);
            cacheUser.deleteBriefUsersByTenantId(SecurityUtils.getTenantId());
            cacheUser.deletePermissionUsersByTenantId(SecurityUtils.getTenantId());
            MDC.put(DataSyncAspect.SYNC_DATA_ID, String.valueOf(newUser.getId()));
            return ResponseEntity.created(new URI("/api/users/" + newUser.getUsername()))
                    .body(newUser);
        }
    }


    /**
     * PUT  /users : Updates an existing User.
     *
     * @param managedUserVM the user to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated user,
     * or with status 400 (Bad Request) if the login or email is already in use,
     * or with status 500 (Internal Server Error) if the user couldn't be updated
     */
    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @PutMapping("/users/{id}")
    @NoRepeatSubmit
    public ResponseEntity<UserDTO> updateUser(@PathVariable Long id, @Valid @RequestBody ManagedUserVM managedUserVM) {
        log.info("[APN: User @{}] REST request to update User : {}", SecurityUtils.getUserId(), managedUserVM);
        StopWatch stopWatch = new StopWatch("main");
        stopWatch.start("[1] update user task");
        UserDTO userDTO = userService.updateUser(id, managedUserVM);
        stopWatch.stop();
        if (Objects.nonNull(userDTO.getPreviousTeamId()) && !userDTO.getPrimaryTeamId().equals(userDTO.getPreviousTeamId())){
            // Primary team changed
            stopWatch.start("[2] sync talent task");
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                talentService.syncTalentsToMQByOwner(id);
            });
            stopWatch.stop();
        }
//        talentService.updateUserNameFromEsDb(userDTO.getId());
        stopWatch.start("[3] delete user list by tenantId redis cache task");
        cacheUser.deleteBriefUsersByTenantId(SecurityUtils.getTenantId());
        stopWatch.stop();
        stopWatch.start("[4] delete permission redis cache task");
        cacheUser.deletePermissionUsersByTenantId(SecurityUtils.getTenantId());
        stopWatch.stop();
        stopWatch.start("[5] delete user account redis cache task");
        redisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + SecurityUtils.getUserId());
        stopWatch.stop();
        stopWatch.start("[6] aop sync user task");
        MDC.put(DataSyncAspect.SYNC_DATA_ID, String.valueOf(id));
        stopWatch.stop();
        log.info("[apn] update user time = [{}ms] \n {}, ids = [{}]", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint(), id);
        return ResponseEntity.ok(userDTO);
    }

    @PostMapping("/users/update-push-time")
    public ResponseEntity<Void> updatePushTime() {
        log.info("[APN: User @{}] REST request to update push time", SecurityUtils.getUserId());
        userService.updatePushTime();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/users/interval")
    public ResponseEntity<Long> getUserPushInterval(@RequestBody UserPushIntervalDTO dto) {
        log.info("[APN: User @{}] REST request to get push interval", SecurityUtils.getUserId());
        return ResponseEntity.ok(userService.getUserPushInterval(dto));
    }

    /**
     * GET  /users : get all users.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users")
    public ResponseEntity<List<UserVM>> getAllUsers(@RequestParam(value = "fullName", required = false) String searchFullName,
                                                    @RequestParam(value = "authorities", required = false) Long roleId,
                                                    @RequestParam(value = "teams", required = false) Long teamId,
                                                    @RequestParam(value = "activated", required = false) Boolean activated,
                                                    @RequestParam(value = "email", required = false) String searchEmail,
                                                    @RequestParam(value = "id", required = false) Long id,
                                                    @ApiParam Pageable pageable) {
        log.info("[APN: User @{}] REST request to get all users", SecurityUtils.getUserId());
        UserSearch userSearch = new UserSearch(pageable).setSearchFullName(searchFullName).setRoleId(roleId)
            .setTeamId(teamId).setActivated(activated).setSearchEmail(searchEmail).setId(id);
        final Page<UserVM> page = userService.getAllManagedUsers(userSearch);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/users");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /activated-users : get all activated users.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/activated-users")
    public ResponseEntity<List<UserDTO>> getAllActivatedUsers(@ApiParam Pageable pageable) {
        log.info("[APN: User @{}] REST request to get all activated users", SecurityUtils.getUserId());
        final Page<UserDTO> page = userService.getAllActivatedUsers(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/activated-users");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * @return a string list of the all of the roles
     */
    @GetMapping("/users/authorities")
    public List<Role> getAuthorities() {
        return userService.getAuthorities();
    }

    @PostMapping("/users/get-all-user-by-ids-or-tenant")
    public ResponseEntity<List<UserBriefDTO>> findAllUserByUserIds(@RequestBody Set<Long> ids){
        log.info("({},{}) REST request to find all user by userIds {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(userService.findAllUserIdByIdsOrTenant(ids));
    }


    /**
     * GET  users/all-brief : get all brief users.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users/all-brief")
    public ResponseEntity<List<UserBriefDTO>> getAllBriefUsers() {
        log.info("[APN: User @{}] REST request to get all brief users", SecurityUtils.getUserId());
        return new ResponseEntity<>(userService.getAllBriefUsers(SecurityUtils.getTenantId()), HttpStatus.OK);
    }

    /**
     * GET  users/all-brief : get all brief users without tenant.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users/all-tenant/emails")
    public ResponseEntity<List<String>> getAllTenantUserEmails() {
        log.info("[APN: User @{}] REST request to get all tenant user emails", SecurityUtils.getUserId());
        return new ResponseEntity<>(userService.getAllValidUserEmails(), HttpStatus.OK);
    }

    /**
     * GET  users/all-brief : get all brief users.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users/all-brief-with-permission/{module}")
    public ResponseEntity<Object> getAllBriefUsersWithPermissionByType(@PathVariable("module") Module module) {
        log.info("[APN: User @{}] REST request to get all brief users with permission , module", SecurityUtils.getUserId(), module);
        return new ResponseEntity<>(userService.getAllBriefUsersWithPermissionByType(module), HttpStatus.OK);
    }

    /**
     * GET  users/all-brief : get all brief users.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users/all-brief-by-tenant-id/{tenantId}")
    public ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByTenantId(@PathVariable("tenantId") Long tenantId) {
        log.info("[APN: User @{}] REST request to get all brief users", SecurityUtils.getUserId());
        return new ResponseEntity<>(userService.getAllBriefUsers(tenantId), HttpStatus.OK);
    }

    @GetMapping("/users/get-all-user-by-team-id/{teamId}")
    public ResponseEntity<List<UserBriefDTO>> findAllActivatedBriefByTeamId(@PathVariable("teamId") Long teamId){
        return ResponseEntity.ok(userService.findAllActivatedBriefByTeamId(teamId));
    }

    /**
     * GET  users/all-brief : get all brief users.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @PostMapping("/users/get-team-info")
    public ResponseEntity<List<TeamInfoVO>> getTeamInfoVOsByUserIds(@RequestBody List<Long> userIds) {
        log.info("[APN: User @{}] REST request to getTeamInfoVOByUserIds", SecurityUtils.getUserId());
        return new ResponseEntity<>(userService.getTeamInfoList(userIds), HttpStatus.OK);
    }

    @GetMapping("/users/all-with-roles-teams")
    public ResponseEntity<List<UserWithRolesAndTeamsDTO>> getAllUsers() {
        log.info("[APN: User @{}] REST request to get all users", SecurityUtils.getUserId());
        return new ResponseEntity<>(userService.getAllUsersWithRolesAndTeams(), HttpStatus.OK);
    }

    @PostMapping("/users/all-brief-by-ids")
    public ResponseEntity<List<UserBriefDTO>> getBriefUsersByIds(@RequestBody List<Long> ids) {
        log.info("[APN: User @{}] REST request to get all brief users by ids: {}", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(userService.getBriefUsersByIds(ids), HttpStatus.OK);
    }

    @PostMapping("/users/all-brief-by-ids/including-inactive")
    public ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByIds(@RequestBody List<Long> ids) {
        log.info("[APN: User @{}] REST request to get all brief users including inactive by ids: {}", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(userService.getAllBriefUsersByIds(ids), HttpStatus.OK);
    }

    @PostMapping("/users/all-by-ids")
    public ResponseEntity<List<User>> findByIds(@RequestBody List<Long> ids) {
        log.info("[APN: User @{}] REST request to get all users by ids: {}", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(userService.getAllUsersByIds(ids), HttpStatus.OK);
    }

    @GetMapping("/users/tenant/{tenantId}/user-countries")
    public ResponseEntity<Set<String>> findUserCountriesByTenantId(@PathVariable Long tenantId) {
        log.info("[APN: User @{}] REST request to get user countries by TenantId: {}", SecurityUtils.getUserId(), tenantId);
        return new ResponseEntity<>(userService.getUserCountriesByTenantId(tenantId), HttpStatus.OK);
    }

    /**
     * GET  users/all-brief : get all brief u.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users/search")
    public ResponseEntity<List<UserBriefDTO>> searchBriefUsers(@RequestParam String name)
    {
        log.info("[APN: User @{}] REST request to get all brief users", SecurityUtils.getUserId());
        return new ResponseEntity<>(userService.searchBriefUsers(name,SecurityUtils.getTenantId()), HttpStatus.OK);
    }

    /**
     * GET  users/all-brief/{username} : get all brief users by username
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @GetMapping("/users/all-brief/{username}")
    public ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByUsername(@Valid @PathVariable String username) {
        log.info("[APN: User @{}] REST request to get all brief users by username : {}", SecurityUtils.getUserId(), username);
        return new ResponseEntity<>(userService.getAllBriefUsersByUsername(username), HttpStatus.OK);
    }

    /**
     * POST  /users/update-credits : manually update all tenants and active users credit for current month
     *
     * @return the ResponseEntity with status 200 (OK)
     */
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @PostMapping("/users/update-credits")
//    @NoRepeatSubmit
//    public ResponseEntity<Void> manuallyUpdateTenantAndActiveUserCredit() {
//        log.info("[APN: User @{}] REST request to manually update tenant and active user credit for current month", SecurityUtils.getUserId());
//        userService.updateTenantAndActiveUserCredit();
//        return ResponseEntity.ok().build();
//    }
//
//    @PostMapping("/users/update-credits/debug")
//    @NoRepeatSubmit
//    public ResponseEntity<Void> manuallyUpdateTenantAndActiveUserCreditDebugOnly() {
//        log.info("[APN: User @{}] REST request to manually update tenant and active user credit for current month debug only", SecurityUtils.getUserId());
//        userService.updateTenantAndActiveUserCreditDebugOnly();
//        return ResponseEntity.ok().build();
//    }

    @GetMapping("/users/common/tenant-param/{paramKey}/value")
    public ResponseEntity<Integer> getTenantParamValue(@PathVariable("paramKey") String paramKey){
        log.info("({},{}) REST request to get tenant param value by key {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), paramKey);
        return ResponseEntity.ok(commonService.getTenantParamValue(paramKey));
    }

    /**
     * Internal feign use only
     * @param paramKey
     * @return
     */
    @GetMapping("/users/common/tenant-param/{paramKey}/tenant/{tenantId}/value")
    ResponseEntity<Integer> getTenantParamValueByTenantId(@PathVariable("paramKey") String paramKey, @PathVariable("tenantId") Long tenantId){
        log.info("({},{}) REST request to get tenant param value by key {}", tenantId, SecurityUtils.getUserId(), paramKey);
        return ResponseEntity.ok(commonService.getTenantParamValueByTenantId(paramKey, tenantId));
    }

    @GetMapping("/users/common/tenant-param/{paramKey}/string-value")
    public ResponseEntity<String> getTenantParamStringValue(@PathVariable("paramKey") String paramKey){
        log.info("({},{}) REST request to get tenant param value by key {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), paramKey);
        return ResponseEntity.ok(commonService.getTenantParamStringValue(paramKey));
    }

    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUserById(@PathVariable("id") Long userId){
        log.info("({},{}) REST request to get user by id {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), userId);
        return ResponseEntity.ok(userService.findOne(userId));
    }

    @PostMapping("/users/get-all-activated-user-by-ids")
    public ResponseEntity<List<User>> findALLByIdInAndActivated(@RequestBody List<Long> ids){
        log.info("({},{}) REST request to find all activated users by ids {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(userService.getExistUsers(ids));
    }

    @GetMapping("/users/get-user-ids/team/{teamId}")
    public ResponseEntity<Set<Long>> findAllUserIdByTeamId(@PathVariable("teamId") Long teamId){
        log.info("({},{}) REST request to find all user ids by teamId {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), teamId);
        return ResponseEntity.ok(userService.findAllUserIdByTeamId(teamId));
    }

    /**
     *  jobDiva use only
     */
    @GetMapping("/users/count-by-user-id-and-role")
    public ResponseEntity<Integer> countByUserIdAndRole(@RequestParam("userId")Long userId, @RequestParam("role") String role){
        log.info("({},{}) REST request to count by user id and role, userId:{}, role:{}", SecurityUtils.getTenantId(), userId, role);
        return ResponseEntity.ok(userRepository.countByUserIdAndRole(userId, role));
    }

    @GetMapping("/users/apnparam/find-by-paramkey")
    public ResponseEntity<ApnParam> findByParamKey(@RequestParam("paramKey") String paramKey, @RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status) {
        return ResponseEntity.ok(apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, tenantId, status));
    }

    @PostMapping("/users/apnparam/save")
    public ResponseEntity<ApnParam> saveApnParam(@RequestBody ApnParam apnParam) {
        return ResponseEntity.ok(apnParamRepository.save(apnParam));
    }

    @GetMapping("/users/find-by-id")
    public ResponseEntity<UserBriefDTO> findById(@RequestParam("id") Long id) {
        log.info("[APN: User @{}] REST request to get one user by id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(Convert.convert(UserBriefDTO.class, userService.findOne(id)), HttpStatus.OK);
    }

    @GetMapping("/users/find-role-by-name")
    public ResponseEntity<Set<Role>> findByName(@RequestParam("roleName") String roleName) {
        return new ResponseEntity<>(roleRepository.findByName(roleName), HttpStatus.OK);
    }

    @GetMapping("/users/keep-live")
    public ResponseEntity<Void> keepLive() {
        return ResponseEntity.ok().build();
    }

    @PostMapping("/users/tenant-admin")
    public ResponseEntity<Void> createTenantAdminUser(@RequestBody TenantAdminUserDto userDTO) {
        userService.createTenantAdminUser(userDTO);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/users/find-by-email")
    public ResponseEntity<UserBriefDTO> findByEmail(@RequestParam("email") String email) {
        log.info("[APN: User @{}] REST request to get one user by email: {}", SecurityUtils.getUserId(), email);
        Optional<User> userOpt = userRepository.findOneByEmail(email);
        UserBriefDTO userBriefDTO = Convert.convert(UserBriefDTO.class, userOpt.orElse(null));
        return new ResponseEntity<>(userBriefDTO, HttpStatus.OK);
    }

    @GetMapping("/users/{tenantId}/page")
    @ApiOperation(value = "Search user List", tags = {"APN V3"})
    public ResponseEntity<List<UserPageVO>> searchUserList(@PathVariable("tenantId") Long tenantId, @PageableDefault @SortDefault(sort = {"id"}, direction = Sort.Direction.ASC) Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN User: User @{}] REST request to search user list : {}", SecurityUtils.getUserId(), pageable);
        Page<UserPageVO> userPageVOPage = userService.searchUserList(tenantId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(userPageVOPage, "/users");
        return new ResponseEntity<>(userPageVOPage.getContent(), headers, HttpStatus.OK);
    }

    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @PutMapping("/users/{userId}/status")
    @NoRepeatSubmit
    @ApiOperation(value = "Update user status", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> updateUserStatus(@PathVariable("userId") Long userId, @RequestBody UserStatusDTO userStatusDTO) {
        log.info("[APN Management: User @{}] REST request to update user status . userId: {},", SecurityUtils.getUserId(), userId);
        userService.updateUserStatus(userId, userStatusDTO);
        MDC.put(DataSyncAspect.SYNC_DATA_ID, String.valueOf(userId));
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @Deprecated(since = "move to sso")
    @PutMapping("/users/timezone")
    @NoRepeatSubmit
    @ApiOperation(value = "修改用户时区配置", tags = {"APN V3"})
    public ResponseEntity<Void> updateTimezoneByUserId(@RequestBody UserTimezoneDTO userTimezoneDTO) {
        log.info("[APN Management: User @{}] REST request to update user timezone config . param: {},", SecurityUtils.getUserId(), userTimezoneDTO);
        userService.updateTimezoneByUserId(SecurityUtils.getUserId(), userTimezoneDTO);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/users/timezone")
    @ApiOperation(value = "获取用户时区配置", tags = {"APN V3"})
    public ResponseEntity<UserTimeZoneVO> getTimezoneByUserId() {
        log.info("[APN Management: User @{}] REST request to get user timezone config .,", SecurityUtils.getUserId());
        return ResponseEntity.ok(userService.getTimezoneByUserId(SecurityUtils.getUserId()));
    }

    @PostMapping("/users/get-timezone-by-ids")
    @ApiOperation(value = "获取用户时区配置", tags = {"APN V3"})
    public ResponseEntity<List<UserTimeZoneVO>> getTimezoneListByUserIdList(@RequestBody List<Long> ids) {
        log.info("[APN Management: User @{}] REST request to get user timezone list config , param = {}.,", SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(userService.getTimezoneListByUserIdList(ids));
    }

    @PutMapping("/users/sync-lark/{syncLark}")
    @NoRepeatSubmit
    @ApiOperation(value = "获取用户时区配置", tags = {"APN V3"})
    public ResponseEntity<Void> updateSyncLark(@PathVariable("syncLark") Integer syncLark) {
        log.info("[APN Management: User @{}] REST request to update syncLark: {}.,", SecurityUtils.getUserId(), syncLark);
        userService.updateSyncLark(SecurityUtils.getUserId(), syncLark);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/users/sync-lark")
    @ApiOperation(value = "获取是否同步lark配置", tags = {"APN V3"})
    public ResponseEntity<Integer> getSyncLark() {
        log.info("[APN Management: User @{}] REST request to get syncLark.,", SecurityUtils.getUserId());
        return ResponseEntity.ok(userService.getSyncLark(SecurityUtils.getUserId()));
    }

    @GetMapping("/relate-job-folders/{jobId}/users")
    public ResponseEntity<List<RelateJobFolderUserInfo>> getRelateJobFolderUsers(@PathVariable("jobId") Long jobId){
        return ResponseEntity.ok(userService.getRelateJobFolderUsers(jobId));
    }

    @GetMapping("/relate-job-folders/{jobId}/tree/users")
    public ResponseEntity<List<RelateJobFolderTeamTreeDTO>> getRelateJobFolderTreeUsers(@PathVariable("jobId") Long jobId){
        return ResponseEntity.ok(userService.getRelateJobFolderTreeUsers(jobId));
    }

    @GetMapping("/team/tree/users")
    public ResponseEntity<List<RelateJobFolderTeamTreeDTO>> getTeamTreeUsers(){
        return ResponseEntity.ok(userService.getTeamTreeUsers());
    }


    /**
     * GET  users/all-brief : get all users with same team from the team provided.
     * @return the ResponseEntity with status 200 (OK) and with body all users
     */
    @PostMapping("/users/same-team")
    public ResponseEntity<Set<Long>> findUserIdsByTeamIds(@RequestBody List<Long> teamIds) {
        log.info("[APN: User @{}] REST request to get all users ids from the user with same team", teamIds);
        return new ResponseEntity<>(userService.findUserIdsInTheSameTeams(teamIds), HttpStatus.OK);
    }

    @Resource
    private UserSkillTagService userSkillTagService;

    @GetMapping("/skill-tag/all")
    public ResponseEntity<List<SkillTagDTO>> getSkillTag(){
        log.info("[APN: User @{}] REST request to get all skill tags", SecurityUtils.getUserId());
        return new ResponseEntity<>(userSkillTagService.getSkillTag(), HttpStatus.OK);
    }

    @PostMapping("/skill-tag")
    public ResponseEntity<List<SkillTagDTO>> updateSkillTag(@RequestBody List<SkillTagDTO> skillTagDTOList){
        log.info("[APN: User @{}] REST request to update user skill tags", SecurityUtils.getUserId());
        return new ResponseEntity<>(userSkillTagService.updateSkillTag(skillTagDTOList), HttpStatus.OK);
    }
}
